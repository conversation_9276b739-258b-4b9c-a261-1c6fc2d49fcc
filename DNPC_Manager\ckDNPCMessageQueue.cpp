#include "..\stdafx.h"

#include "ckDNPCMessageQueue.h"

void ckDNPCMessageQueue::Create(int max_message_object)
{
	m_AllocedMessagePool.Create(max_message_object);
	if( FALSE == InitializeCriticalSectionAndSpinCount( &cs, (0x80000000)|2000 ) )
		throw "" ;
}

void ckDNPCMessageQueue::Release(bool safe_type)
{
	//Clear Pool
	if(true == safe_type)
	{
		EnterCriticalSection(&cs);

		while(m_MessageQueue.empty() == false)
		{
			m_AllocedMessagePool.ReleasePointer(m_MessageQueue.top());
			m_MessageQueue.pop();
		}

		LeaveCriticalSection(&cs);
	}
	else
	{
		//Release Alloced Pool
		if(true == safe_type)
			m_AllocedMessagePool.Release();
	}
}

//ckDNPCMessage* ckDNPCMessageQueue::Top()

//ckDNPCMessage* ckDNPCMessageQueue::Pop()


//void ckDNPCMessageQueue::Push(unsigned short message, unsigned short time_gap, unsigned short from_id, ckDNPCInterface* pTargetDNPC, unsigned int param)


//void ckDNPCMessageQueue::Push(unsigned short message, unsigned short time_gap, unsigned short from_id, unsigned short to_id, unsigned char life_count, unsigned char status_count, unsigned int param)


//ckDNPCMessage* ckDNPCMessageQueue::GetMessageObj()


//void ckDNPCMessageQueue::Push(ckDNPCMessage* msg)


//void ckDNPCMessageQueue::ReleaseMessage(ckDNPCMessage* pMsg)
