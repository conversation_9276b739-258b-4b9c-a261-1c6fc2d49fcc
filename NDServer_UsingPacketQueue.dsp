# Microsoft Developer Studio Project File - Name="NDServer_UsingPacketQueue" - Package Owner=<4>
# Microsoft Developer Studio Generated Build File, Format Version 6.00
# ** DO NOT EDIT **

# TARGTYPE "Win32 (x86) Console Application" 0x0103

CFG=NDSERVER_USINGPACKETQUEUE - WIN32 RD_NX_IN_TS
!MESSAGE This is not a valid makefile. To build this project using NMAKE,
!MESSAGE use the Export Makefile command and run
!MESSAGE 
!MESSAGE NMAKE /f "NDServer_UsingPacketQueue.mak".
!MESSAGE 
!MESSAGE You can specify a configuration when running NMAKE
!MESSAGE by defining the macro CFG on the command line. For example:
!MESSAGE 
!MESSAGE NMAKE /f "NDServer_UsingPacketQueue.mak" CFG="NDSERVER_USINGPACKETQUEUE - WIN32 RD_NX_IN_TS"
!MESSAGE 
!MESSAGE Possible choices for configuration are:
!MESSAGE 
!MESSAGE "NDServer_UsingPacketQueue - Win32 Release" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_IDC" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_METEL" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_VINA" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_ACCLAIM" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_NX_IN_TS" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_ROC" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_OPEN_TEST" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_OT_INTS" (based on "Win32 (x86) Console Application")
!MESSAGE "NDServer_UsingPacketQueue - Win32 Rd_RUSSIA" (based on "Win32 (x86) Console Application")
!MESSAGE 

# Begin Project
# PROP AllowPerConfigDependencies 0
# PROP Scc_ProjName ""$/NDServer_UsingPacketQueue", MDBAAAAA"
# PROP Scc_LocalPath "."
CPP=cl.exe
RSC=rc.exe

!IF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Release"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "Release"
# PROP BASE Intermediate_Dir "Release"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "Release"
# PROP Intermediate_Dir "Release"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /YX /FD /c
# ADD CPP /nologo /MT /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_TRACER_ON_" /D "_INNER_TEST_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /machine:I386
# ADD LINK32 kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib winmm.lib ws2_32.lib Iphlpapi.lib /nologo /subsystem:console /machine:I386
# SUBTRACT LINK32 /pdb:none

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_IDC"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_IDC"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_IDC"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_IDC"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_IDC"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /ZI /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_TRACE_ON_" /D "_INNER_TEST_NO" /D "_ITEM_ISSUE_GRADE_ZERO_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_PD_COMPANY_NEXON_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_UsingPacketQueue_idc.exe"
# SUBTRACT BASE LINK32 /pdb:none
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_idc.exe"
# SUBTRACT LINK32 /pdb:none /map

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_METEL"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_METEL"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_METEL"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_METEL"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_METEL"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /ZI /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_IDC_TEST_" /D "_ITEM_ISSUE_GRADE_ZERO_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_COMPANY_METEL_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_UsingPacketQueue_idctest.exe"
# SUBTRACT BASE LINK32 /pdb:none
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_Metel.exe"
# SUBTRACT LINK32 /pdb:none

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_VINA"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_VINA0"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_VINA0"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_VINA0"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_VINA0"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_ITEM_ISSUE_GRADE_ZERO_" /D "_COMPANY_METEL_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_COMPANY_VINA_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_metel.exe"
# SUBTRACT BASE LINK32 /pdb:none
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_Vina.exe"
# SUBTRACT LINK32 /pdb:none

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_ACCLAIM"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_ITEM_ISSUE_GRADE_ZERO_" /D "_COMPANY_VINA_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_COMPANY_ACCLAIM_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_vina.exe"
# SUBTRACT BASE LINK32 /pdb:none
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_acclaim.exe"
# SUBTRACT LINK32 /pdb:none

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_NX_IN_TS"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /ZI /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_IDC_TEST_" /D "_ITEM_ISSUE_GRADE_ZERO_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /ZI /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_PD_COMPANY_NXINTS_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_idctest.exe"
# SUBTRACT BASE LINK32 /pdb:none
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_nxints.exe"
# SUBTRACT LINK32 /pdb:none

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_ROC"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_ROC"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_ROC"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_ROC"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_ROC"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_ITEM_ISSUE_GRADE_ZERO_" /D "_COMPANY_ACCLAIM_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_COMPANY_JAPAN_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_acclaim.exe"
# SUBTRACT BASE LINK32 /pdb:none
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_RocWorks.exe"
# SUBTRACT LINK32 /pdb:none

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_OPEN_TEST"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_ITEM_ISSUE_GRADE_ZERO_" /D "_PD_COMPANY_NEXON_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_PD_OPEN_TEST_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_idc.exe"
# SUBTRACT BASE LINK32 /pdb:none /map
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_opentest.exe"
# SUBTRACT LINK32 /pdb:none /map

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_OT_INTS"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_OT_INTS"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_OT_INTS"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_OT_INTS"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_OT_INTS"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_ITEM_ISSUE_GRADE_ZERO_" /D "_PD_OPEN_TEST_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_PD_OT_INTS_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_opentest.exe"
# SUBTRACT BASE LINK32 /pdb:none /map
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_ot_ints.exe"
# SUBTRACT LINK32 /pdb:none /map

!ELSEIF  "$(CFG)" == "NDServer_UsingPacketQueue - Win32 Rd_RUSSIA"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_RUSSIA"
# PROP BASE Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_RUSSIA"
# PROP BASE Ignore_Export_Lib 0
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "NDServer_UsingPacketQueue___Win32_Rd_RUSSIA"
# PROP Intermediate_Dir "NDServer_UsingPacketQueue___Win32_Rd_RUSSIA"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
# ADD BASE CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_COMPANY_ACCLAIM_" /FR /YX /FD /c
# ADD CPP /nologo /MT /W3 /WX /GX /Zi /Od /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /D "_COMPANY_RUSSIA_" /FR /YX /FD /c
# ADD BASE RSC /l 0x412 /d "NDEBUG"
# ADD RSC /l 0x412 /d "NDEBUG"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_acclaim.exe"
# SUBTRACT BASE LINK32 /pdb:none
# ADD LINK32 winmm.lib ws2_32.lib Iphlpapi.lib Dbghelp.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /debug /machine:I386 /out:"Release/NDServer_russia.exe"
# SUBTRACT LINK32 /pdb:none

!ENDIF 

# Begin Target

# Name "NDServer_UsingPacketQueue - Win32 Release"
# Name "NDServer_UsingPacketQueue - Win32 Rd_IDC"
# Name "NDServer_UsingPacketQueue - Win32 Rd_METEL"
# Name "NDServer_UsingPacketQueue - Win32 Rd_VINA"
# Name "NDServer_UsingPacketQueue - Win32 Rd_ACCLAIM"
# Name "NDServer_UsingPacketQueue - Win32 Rd_NX_IN_TS"
# Name "NDServer_UsingPacketQueue - Win32 Rd_ROC"
# Name "NDServer_UsingPacketQueue - Win32 Rd_OPEN_TEST"
# Name "NDServer_UsingPacketQueue - Win32 Rd_OT_INTS"
# Name "NDServer_UsingPacketQueue - Win32 Rd_RUSSIA"
# Begin Group "Global"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Global\Billing_Define.h
# End Source File
# Begin Source File

SOURCE=.\Global\ckSingleton.h
# End Source File
# Begin Source File

SOURCE=.\Global\ComTcp_packets.h
# End Source File
# Begin Source File

SOURCE=.\Global\CriticalSection.h
# End Source File
# Begin Source File

SOURCE=.\Global\eod_base_file.cpp
# End Source File
# Begin Source File

SOURCE=.\Global\eod_base_file.h
# End Source File
# Begin Source File

SOURCE=.\Global\FileInput.cpp
# End Source File
# Begin Source File

SOURCE=.\Global\FileInput.h
# End Source File
# Begin Source File

SOURCE=.\Global\FileLog.h
# End Source File
# Begin Source File

SOURCE=.\Global\gDefine.h
# End Source File
# Begin Source File

SOURCE=.\Global\gGlobal.h
# End Source File
# Begin Source File

SOURCE=.\Global\Global_Define.h
# End Source File
# Begin Source File

SOURCE=.\Global\Global_Function.cpp
# End Source File
# Begin Source File

SOURCE=.\Global\Global_Function.h
# End Source File
# Begin Source File

SOURCE=.\Global\NDThread.cpp
# End Source File
# Begin Source File

SOURCE=.\Global\NDThread.h
# End Source File
# Begin Source File

SOURCE=.\Global\NDUtilClass.cpp
# End Source File
# Begin Source File

SOURCE=.\Global\NDUtilClass.h
# End Source File
# End Group
# Begin Group "Packets"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Packets\Game_packets.h
# End Source File
# Begin Source File

SOURCE=.\Packets\Packet_for_Battle.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_combat.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_Common.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_ctrl_gms.h
# End Source File
# Begin Source File

SOURCE=.\Packets\Packet_for_Event.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_ex_battle_room.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_GGAuth.h
# End Source File
# Begin Source File

SOURCE=.\Packets\Packet_for_GMS.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_items_trade.h
# End Source File
# Begin Source File

SOURCE=.\Packets\Packet_for_ItemShop.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_Level.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_LifeEtc.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_lobby.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_login.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_nic.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_organization.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_party.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_quest.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_skill.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_svrmove.h
# End Source File
# Begin Source File

SOURCE=.\Packets\packet_for_Zone_Monster.h
# End Source File
# End Group
# Begin Group "Virtual Memory"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\VirtualMemory\VMManager.cpp
# End Source File
# Begin Source File

SOURCE=.\VirtualMemory\VMManager.h
# End Source File
# End Group
# Begin Group "Network"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Network\Network.cpp
# End Source File
# Begin Source File

SOURCE=.\Network\Network.h
# End Source File
# End Group
# Begin Group "Crypto"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Crypto\crypto.h
# End Source File
# Begin Source File

SOURCE=.\Crypto\lump.dat
# End Source File
# End Group
# Begin Group "Environment"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Environment\Environment_Read.cpp
# End Source File
# Begin Source File

SOURCE=.\Environment\Environment_Read.h
# End Source File
# End Group
# Begin Group "Level_Luck"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Level_Luck\LevelManager.cpp
# End Source File
# Begin Source File

SOURCE=.\Level_Luck\LevelManager.h
# End Source File
# End Group
# Begin Group "Skill"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Skill\SkillManager.cpp
# End Source File
# Begin Source File

SOURCE=.\Skill\SkillManager.h
# End Source File
# End Group
# Begin Group "Quest"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Quest\Quest_Define.h
# End Source File
# Begin Source File

SOURCE=.\Quest\ServerQuest.cpp
# End Source File
# Begin Source File

SOURCE=.\Quest\ServerQuest.h
# End Source File
# Begin Source File

SOURCE=.\Quest\user_quest.cpp
# End Source File
# Begin Source File

SOURCE=.\Quest\user_quest.h
# End Source File
# Begin Source File

SOURCE=.\Quest\user_quest2.cpp
# End Source File
# End Group
# Begin Group "Item"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Item\Item.h
# End Source File
# Begin Source File

SOURCE=.\Item\Item_Manager.cpp
# End Source File
# Begin Source File

SOURCE=.\Item\Item_Manager.h
# End Source File
# Begin Source File

SOURCE=.\Item\PkgMgr.cpp
# End Source File
# Begin Source File

SOURCE=.\Item\PkgMgr.h
# End Source File
# Begin Source File

SOURCE=.\Item\ScriptParser.h
# End Source File
# Begin Source File

SOURCE=.\Item\ScriptParser_ItemShop.cpp
# End Source File
# End Group
# Begin Group "PathFinder"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\PathFinder\PathFinder.cpp
# End Source File
# Begin Source File

SOURCE=.\PathFinder\PathFinder.h
# End Source File
# End Group
# Begin Group "NPC"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\NPC\CollectNPC.cpp
# End Source File
# Begin Source File

SOURCE=.\NPC\CollectNPC.h
# End Source File
# Begin Source File

SOURCE=.\NPC\NPCBase.h
# End Source File
# Begin Source File

SOURCE=.\NPC\SNpcManager.cpp
# End Source File
# Begin Source File

SOURCE=.\NPC\SNpcManager.h
# End Source File
# Begin Source File

SOURCE=.\NPC\StaticNpc.cpp
# End Source File
# Begin Source File

SOURCE=.\NPC\StaticNpc.h
# End Source File
# End Group
# Begin Group "Zone"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Zone\Zone.cpp
# End Source File
# Begin Source File

SOURCE=.\Zone\Zone.h
# End Source File
# End Group
# Begin Group "PacketQueue"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\PacketQueue\Packet_Queue.cpp
# End Source File
# Begin Source File

SOURCE=.\PacketQueue\Packet_Queue.h
# End Source File
# End Group
# Begin Group "PacketProcess"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\PacketProcess\PING_Thread.cpp
# End Source File
# Begin Source File

SOURCE=.\PacketProcess\Proc_General.cpp
# End Source File
# Begin Source File

SOURCE=.\PacketProcess\Proc_Items.cpp
# End Source File
# Begin Source File

SOURCE=.\PacketProcess\Proc_LevelSkill.cpp
# End Source File
# Begin Source File

SOURCE=.\PacketProcess\Proc_Lobby.cpp
# End Source File
# Begin Source File

SOURCE=.\PacketProcess\Proc_Quest.cpp
# End Source File
# End Group
# Begin Group "DNPC_Manager"

# PROP Default_Filter ""
# Begin Group "DNPC_Script"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_00_00_Type_70.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_02_01_Type_101.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_02_02_Type_102.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_02_03_Type_103.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_02_04_Type_104.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_01_Type_105.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_02_Type_106.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_03_Type_107.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_04_Type_108.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_05_Type_109.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_06_Type_110.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_07_Type_111.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_08_Type_112.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_09_Type_113.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_102_Type_206.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_104_Type_208.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_10_Type_114.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_11_Type_115.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_12_Type_116.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_13_Type_117.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_14_Type_118.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_15_Type_119.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_16_Type_120.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_17_Type_121.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_22_Type_126.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_23_Type_127.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_25_Type_129.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_26_Type_130.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_27_Type_131.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_30_Type_134.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_40_Type_144.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_50_Type_154.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_53_Type_157.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_54_Type_158.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_57_Type_161.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_59_Type_163.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_60_Type_164.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_61_Type_165.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_63_Type_167.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_69_Type_173.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_70_Type_174.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_74_Type_178.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_75_Type_179.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_03_99_Type_203.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_05_04_Type_219.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_05_05_Type_220.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_05_07_Type_222.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_05_12_Type_227.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_05_20_Type_235.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_06_01_Type_238.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_06_05_Type_242.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_07_01A_ISTJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_08_01B_ISTJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_09_03_Type_262.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_09_04_Type_263.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_09_12_Type_271.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_09_NewType.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_10_01_Type_273.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_10_02B_ISFJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_11_03A_INFJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_12_03B_INFJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_13_04A_INTJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_14_04B_INTJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_16_05B_ISTP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_18_06B_ISFP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_19_07A_INFP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_20_07B_INFP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_21_08A_INTP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_22_08B_INTP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_23_09A_ESTP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_24_09B_ESTP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_25_10A_ESFP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_26_10B_ESFP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_277_die_help_Human.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_278_Heal.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_279_SP1.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_27_11A_ENFP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_28_11B_ENFP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_29_12A_ENTP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_30_12B_ENTP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_31_13A_ESTJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_33_14A_ESFJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_35_15A_ENFJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_36_15B_ENFJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_37_16A_ENTJ.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_40_Heal_Type.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_42_AI_02_Type.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_51_Raid_Top.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_52_Raid_Middle.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_53_Tolerance.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_70_manor.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_81_Normal.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_82_Key.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_83_Transform.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_84_HP.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_85_Aggressive.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_86_AggressiveDummy.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_90_Object.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_96_Patrol06_Monster.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_Animal.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_Animal_Bear.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_Animal_BloodWolf.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_Animal_Fox.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_Animal_Leopard.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_Animal_Tiger.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_Animal_Wolf.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_bookhae_field_boss.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_bookhae_field_raid_a.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_bookhae_field_raid_b.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_bookhae_guard_monster.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\DNPC_Script\DNPC_Patrol01.cpp
# End Source File
# End Group
# Begin Source File

SOURCE=.\DNPC_Manager\ckAllocedPool.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPC.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPC.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPC2.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCAllocedPool.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCAllocedPool.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCMacro.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCMacro.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCMessage.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCMessageQueue.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCMessageQueue.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCPathManager.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCPathManager.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCPool.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCPool.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCProcThread.cpp
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCProcThread.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckDNPCSpecies.h
# End Source File
# Begin Source File

SOURCE=.\DNPC_Manager\ckSpawnInfo.h
# End Source File
# End Group
# Begin Group "LogSvr"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\LogSvr\Global_Log_Define.h
# End Source File
# Begin Source File

SOURCE=.\LogSvr\Global_Log_Packet.h
# End Source File
# Begin Source File

SOURCE=.\LogSvr\LogSave_Thread.cpp
# End Source File
# Begin Source File

SOURCE=.\LogSvr\LogSvrLink.cpp
# End Source File
# Begin Source File

SOURCE=.\LogSvr\LogSvrLink.h
# End Source File
# End Group
# Begin Group "Scheduler"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Scheduler\Scheduler.cpp
# End Source File
# Begin Source File

SOURCE=.\Scheduler\Scheduler.h
# End Source File
# End Group
# Begin Group "User"

# PROP Default_Filter ""
# Begin Group "Community"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\User\ExceptionBattle.cpp
# End Source File
# Begin Source File

SOURCE=.\User\ExceptionBattle.h
# End Source File
# Begin Source File

SOURCE=.\User\NDParty.cpp
# End Source File
# Begin Source File

SOURCE=.\User\NDParty.h
# End Source File
# Begin Source File

SOURCE=.\User\Organization.cpp
# End Source File
# Begin Source File

SOURCE=.\User\Organization.h
# End Source File
# End Group
# Begin Group "Combat"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\User\Combat.cpp
# End Source File
# Begin Source File

SOURCE=.\User\Combat.h
# End Source File
# Begin Source File

SOURCE=.\User\CombatModule.cpp
# End Source File
# Begin Source File

SOURCE=.\User\CombatModule.h
# End Source File
# Begin Source File

SOURCE=.\User\CombatProc.cpp
# End Source File
# Begin Source File

SOURCE=.\User\CombatProc.h
# End Source File
# Begin Source File

SOURCE=.\User\CombatRecord.cpp
# End Source File
# Begin Source File

SOURCE=.\User\CombatRecord.h
# End Source File
# Begin Source File

SOURCE=.\User\CombatStruct.h
# End Source File
# End Group
# Begin Group "TaxSystem"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\User\TaxSystem.cpp
# End Source File
# Begin Source File

SOURCE=.\User\TaxSystem.h
# End Source File
# End Group
# Begin Group "LTS"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\User\Location_Tracking_System.cpp
# End Source File
# Begin Source File

SOURCE=.\User\Location_Tracking_System.h
# End Source File
# Begin Source File

SOURCE=.\User\LocationStruct.h
# End Source File
# End Group
# Begin Source File

SOURCE=.\User\MatchSystem.cpp
# End Source File
# Begin Source File

SOURCE=.\User\MatchSystem.h
# End Source File
# Begin Source File

SOURCE=.\User\User.cpp
# End Source File
# Begin Source File

SOURCE=.\User\User.h
# End Source File
# Begin Source File

SOURCE=.\User\User_Battle_Part.cpp
# End Source File
# Begin Source File

SOURCE=.\User\User_Effect_Part.cpp
# End Source File
# Begin Source File

SOURCE=.\User\User_Item_Part.cpp
# End Source File
# Begin Source File

SOURCE=.\User\UserSkill.cpp
# End Source File
# Begin Source File

SOURCE=.\User\UserSkill.h
# End Source File
# Begin Source File

SOURCE=.\User\UserTimer.cpp
# End Source File
# Begin Source File

SOURCE=.\User\UserTimer.h
# End Source File
# End Group
# Begin Group "NDExceptionReport"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\NDExceptionReport\NDExceptionReport.cpp
# End Source File
# Begin Source File

SOURCE=.\NDExceptionReport\NDExceptionReport.h
# End Source File
# End Group
# Begin Group "Server Communication"

# PROP Default_Filter ""
# Begin Source File

SOURCE=".\Server Communication\Server_Com.cpp"
# End Source File
# Begin Source File

SOURCE=".\Server Communication\Server_Com.h"
# End Source File
# Begin Source File

SOURCE=".\Server Communication\Server_Com_TCP.cpp"
# End Source File
# End Group
# Begin Group "Ranking"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Ranking\Ranking.cpp
# End Source File
# Begin Source File

SOURCE=.\Ranking\Ranking.h
# End Source File
# End Group
# Begin Group "Event"

# PROP Default_Filter ""
# Begin Group "GEvent"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Event\GEvent\GameEvent.cpp
# End Source File
# Begin Source File

SOURCE=.\Event\GEvent\GameEvent.h
# End Source File
# Begin Source File

SOURCE=.\Event\GEvent\GameEventParser.cpp
# End Source File
# Begin Source File

SOURCE=.\Event\GEvent\GameEventParser.h
# End Source File
# Begin Source File

SOURCE=.\Event\GEvent\GEventStructure.cpp
# End Source File
# Begin Source File

SOURCE=.\Event\GEvent\GEventStructure.h
# End Source File
# End Group
# Begin Group "PKEvent"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Event\PKEvent\PkEvent.cpp
# End Source File
# Begin Source File

SOURCE=.\Event\PKEvent\PkEvent.h
# End Source File
# Begin Source File

SOURCE=.\Event\PKEvent\PkEventScriptParser.cpp
# End Source File
# Begin Source File

SOURCE=.\Event\PKEvent\PkEventScriptParser.h
# End Source File
# End Group
# End Group
# Begin Group "LogWnd"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\LogWnd\LogWnd.h
# End Source File
# Begin Source File

SOURCE=.\LogWnd\mLogWnd.lib
# End Source File
# Begin Source File

SOURCE=.\LogWnd\uLogWnd.lib
# End Source File
# End Group
# Begin Group "Shutdown"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Shutdown\FatiguePenaltyList.txt
# End Source File
# Begin Source File

SOURCE=.\Shutdown\FatiguePenaltyListParser.cpp
# End Source File
# Begin Source File

SOURCE=.\Shutdown\FatiguePenaltyListParser.h
# End Source File
# End Group
# Begin Group "SmallNet"

# PROP Default_Filter ""
# Begin Group "PktProc"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\SmallNet\PktProc\_PkHdr.cpp
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktProc\_PktDef.h
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktProc\Echo.cpp
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktProc\Echo.h
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktProc\NetEventProc.cpp
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktProc\PktBilling.cpp
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktProc\PktBilling.h
# End Source File
# End Group
# Begin Source File

SOURCE=.\SmallNet\Packet.h
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktMgr.cpp
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktMgr.h
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktQue.cpp
# End Source File
# Begin Source File

SOURCE=.\SmallNet\PktQue.h
# End Source File
# Begin Source File

SOURCE=.\SmallNet\SmallNet.cpp
# End Source File
# Begin Source File

SOURCE=.\SmallNet\SmallNet.h
# End Source File
# Begin Source File

SOURCE=.\SmallNet\Sock.cpp
# End Source File
# Begin Source File

SOURCE=.\SmallNet\Sock.h
# End Source File
# End Group
# Begin Group "Acclaim"

# PROP Default_Filter ""
# Begin Group "AD"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\nfofile\Acclaim_AD_RewardList.txt
# End Source File
# Begin Source File

SOURCE=.\Acclaim\AD\Acclaim_AD_RewardListParser.cpp
# End Source File
# Begin Source File

SOURCE=.\Acclaim\AD\Acclaim_AD_RewardListParser.h
# End Source File
# End Group
# End Group
# Begin Group "GameGuard"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\GameGuard\ggsrv25.cpp
# End Source File
# Begin Source File

SOURCE=.\GameGuard\ggsrv25.h
# End Source File
# End Group
# Begin Group "Headers"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\hdr_acclaim.h
# End Source File
# Begin Source File

SOURCE=.\hdr_japan.h
# End Source File
# Begin Source File

SOURCE=.\hdr_metel.h
# End Source File
# Begin Source File

SOURCE=.\hdr_nexon.h
# End Source File
# Begin Source File

SOURCE=.\hdr_nx_innertest.h
# End Source File
# Begin Source File

SOURCE=.\hdr_open_test.h
# End Source File
# Begin Source File

SOURCE=.\hdr_ot_inner.h
# End Source File
# Begin Source File

SOURCE=.\hdr_russia.h
# End Source File
# Begin Source File

SOURCE=.\hdr_vina.h
# End Source File
# Begin Source File

SOURCE=.\StdAfx.h
# End Source File
# End Group
# Begin Group "GameGuard_New0228"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\GameGuard_New0228\ggsrv25.h
# End Source File
# Begin Source File

SOURCE=.\GameGuard_New0228\ggsrv25_2.cpp
# End Source File
# End Group
# Begin Group "Apex"

# PROP Default_Filter ""
# Begin Source File

SOURCE=.\Apex\ApexProxy.cpp
# End Source File
# Begin Source File

SOURCE=.\Apex\ApexProxy.h
# End Source File
# Begin Source File

SOURCE=.\Apex\NDApexFunction.cpp
# End Source File
# Begin Source File

SOURCE=.\Apex\NDApexFunction.h
# End Source File
# End Group
# Begin Source File

SOURCE=.\mFileLog.dll
# End Source File
# Begin Source File

SOURCE=.\NineDragonsServer.cpp
# End Source File
# Begin Source File

SOURCE=.\ReadMe.txt
# End Source File
# Begin Source File

SOURCE=.\restore_contents.cpp
# End Source File
# Begin Source File

SOURCE=.\Temp.txt
# End Source File
# Begin Source File

SOURCE=.\mFileLog.lib
# End Source File
# End Target
# End Project
