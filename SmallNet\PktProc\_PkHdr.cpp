#include "../../stdafx.h"

#ifdef	_NXPCB_AWARD_200703_

#include <WinSock2.h>
//#include <WS2tcpip.h>
//#include <MSWSock.h>
#include "../../Global/gGlobal.h"
#include "../Packet.h"
#include "../PktMgr.h"
#include "../PktQue.h"
#include "../Sock.h"
#include "../SmallNet.h"
#include "./_PktDef.h"

#if defined(_PAYLETTER_NETWORK_)
void PKTHDR_S2C_ECHO_ACK(CNetClient*, CPacket*);
void PKTHDR_GAME_BILL_CMD_GAMESTART_REP(CNetClient*, CPacket*);
void PKTHDR_GAME_BILL_CMD_GAMEEND_REP(CNetClient*, CPacket*);
void PKTHDR_GAME_BILL_CMD_ALLDISCONNECT_REP(CNetClient*, CPacket*);
void PKTHDR_GAME_BILL_CMD_GETBALANCE_REP(CNetClient*, CPacket*);
void PKTHDR_GAME_BILL_CMD_PING_REP(CNetClient*, CPacket*);

void PKTHDR_GAME_BILL_CMD_DISCONNECT_REQ(CNetClient*, CPacket*);
void PKTHDR_GAME_BILL_CMD_ISALIVE_REQ(CNetClient*, CPacket*);
void PKTHDR_GAME_BILL_CMD_COMEEXPIRE_REQ(CNetClient*, CPacket*);

void PKTHDR_GAME_BILL_CMD_STARTDEDUCTION_REP(CNetClient*, CPacket*);
void PKTHDR_GAME_BILL_CMD_ENDDEDUCTION_REP(CNetClient*, CPacket*);

BEGIN_PKT_HANDLER_MAP(PktHandler)
	DECLARE_HANDLER(S2C_ECHO_ACK)
	DECLARE_HANDLER(GAME_BILL_CMD_GAMESTART_REP)
	DECLARE_HANDLER(GAME_BILL_CMD_GAMEEND_REP)
	DECLARE_HANDLER(GAME_BILL_CMD_ALLDISCONNECT_REP)
	DECLARE_HANDLER(GAME_BILL_CMD_GETBALANCE_REP)
	DECLARE_HANDLER(GAME_BILL_CMD_PING_REP)
	DECLARE_HANDLER(GAME_BILL_CMD_DISCONNECT_REQ)
	DECLARE_HANDLER(GAME_BILL_CMD_ISALIVE_REQ)
	DECLARE_HANDLER(GAME_BILL_CMD_COMEEXPIRE_REQ)
	DECLARE_HANDLER(GAME_BILL_CMD_STARTDEDUCTION_REP)
	DECLARE_HANDLER(GAME_BILL_CMD_ENDDEDUCTION_REP)
END_PKT_HANDLER_MAP

#elif defined(_NXPCB_AWARD_200703_)
//void PKTHDR_NEXONPCB_LOGINOUT(CNetClient*, CPacket*);

BEGIN_PKT_HANDLER_MAP(PktHandler)
	//DECLARE_HANDLER(NEXONPCB_LOGINOUT)
END_PKT_HANDLER_MAP
#endif

#endif //_NXPCB_AWARD_200703_