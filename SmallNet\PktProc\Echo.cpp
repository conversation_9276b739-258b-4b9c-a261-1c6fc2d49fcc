#include "../../stdafx.h"

#ifdef	_NXPCB_AWARD_200703_

#include <WinSock2.h>
//#include <WS2tcpip.h>
//#include <MSWSock.h>
#include "../../Global/gGlobal.h"
#include "../Packet.h"
#include "../PktMgr.h"
#include "../PktQue.h"
#include "../Sock.h"
#include "../SmallNet.h"
#include "./_PktDef.h"
#include "./Echo.h"

#if defined(_PAYLETTER_NETWORK_)

void C2SEchoReq() {
	// send sample
	PKT_C2S_ECHO_REQ req;
	strcpy(req.pszEcho, "echo test");
	CPacket* pSendPkt = GetPkt(CELL_256);
	*pSendPkt << req;
	CSmallNet::GetInstance()->Send(C2S_ECHO_REQ, pSendPkt);
}

void PKTHDR_S2C_ECHO_ACK(CNetClient* pClient, CPacket* pRecvPkt) {
	static int iCnt = 0;
	
	// recv sample
	PKT_S2C_ECHO_ACK ack;
	*pRecvPkt >> ack.pszEcho;
	printf("%d %s \n", ++iCnt, ack.pszEcho);

	Sleep(1);
	C2SEchoReq();

	return;
}

#endif
#endif //_NXPCB_AWARD_200703_
