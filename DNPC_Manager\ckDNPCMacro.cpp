#include "../StdAfx.h"
#include "ckDNPCPool.h"
#include "ckDNPCMacro.h"

unsigned char GetDnpcCurVitalRate(unsigned short dnpc_id)
{
	ckDNPCInterface* pDNPC = ckDNPCPool::GetObj().GetDNPC(dnpc_id);
	if(NULL != pDNPC)
		return pDNPC->GetVitalRate();
	else
		return 0;
}

unsigned short GetDNPCLevel(unsigned short dnpc_id)
{
	ckDNPCInterface* pDNPC = ckDNPCPool::GetObj().GetDNPC(dnpc_id);
	if(NULL != pDNPC)
		return pDNPC->GetLevel();
	else
		return 0;
}

char GetDNPCParty(unsigned short dnpc_id)
{
	ckDNPCInterface* pDNPC = ckDNPCPool::GetObj().GetDNPC(dnpc_id);
	if(NULL != pDNPC)
		return pDNPC->GetCharParty();
	else
		return 0;
}

bool DNPCCheckSpell(unsigned short dnpc_id, unsigned short spell_id, unsigned char spell_step)
{
	ckDNPCInterface* pDNPC = ckDNPCPool::GetObj().GetDNPC(dnpc_id);
	if(NULL == pDNPC)
		return false;

	return pDNPC->IsSpellOn(spell_id, spell_step);
}
