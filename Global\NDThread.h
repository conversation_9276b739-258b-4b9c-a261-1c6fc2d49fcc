#ifndef _ND_SERVER_THREAD_BASE_CLASS_2004_03_21_LKAJSD8FUA9S8DFAKJ__
#define _ND_SERVER_THREAD_BASE_CLASS_2004_03_21_LKAJSD8FUA9S8DFAKJ__

typedef unsigned __stdcall TPTHREAD (void *) ;

class CThread
{
protected:
	HANDLE		m_hHandle ;
	unsigned	m_threadID ;
	
public:
	virtual ~CThread() {
		if( m_hHandle )
			CloseHandle( m_hHandle ) ;
		m_hHandle = NULL ;
	}
	
	CThread() : m_hHandle(NULL), m_threadID(0U)
	{}
	
	bool start_thread( TPTHREAD * pFunc, void * pValue = NULL ) ;
	HANDLE get_handle() { return m_hHandle ; }
} ;


#endif

