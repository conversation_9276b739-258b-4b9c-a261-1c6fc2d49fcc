#pragma once

#ifndef _LOGWND_H_
#define _LOGWND_H_

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#include <windows.h>
#include <tchar.h>

#ifdef LOGWNDDLL_EXPORTS
__declspec(dllexport) BOOL LOGWND(LPCTSTR pszFormat, ...);
#else
__declspec(dllimport) BOOL LOGWND(LPCTSTR pszFormat, ...);
#endif

#ifndef LOGWNDDLL_EXPORTS
	#ifndef _LOG_WND_LIB_
	#define _LOG_WND_LIB_
		#ifdef _UNICODE
			#pragma comment(lib, "uLogWnd.lib")
		#else
			#pragma comment(lib, "mLogWnd.lib")
		#endif
	#endif
#endif

#endif //_LOGWND_H_
