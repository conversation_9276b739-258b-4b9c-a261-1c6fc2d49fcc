﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Global">
      <UniqueIdentifier>{f7058393-cade-4d8b-ade6-6c6fb013a1d2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Packets">
      <UniqueIdentifier>{d500df36-496c-46f6-89a7-591214bd183e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Virtual Memory">
      <UniqueIdentifier>{a304d9f1-def5-408d-9ecb-47905f492aea}</UniqueIdentifier>
    </Filter>
    <Filter Include="Network">
      <UniqueIdentifier>{84204cf9-e6df-4581-aa0a-fc5a84df1374}</UniqueIdentifier>
    </Filter>
    <Filter Include="Crypto">
      <UniqueIdentifier>{8696e0ca-0091-431e-9417-0aedcfdfb496}</UniqueIdentifier>
    </Filter>
    <Filter Include="Environment">
      <UniqueIdentifier>{b98e5875-41d3-4b37-a8e1-0518a7d25b6d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Level_Luck">
      <UniqueIdentifier>{a6d75a15-fc63-48c4-b7f5-4a6c9b793db8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Skill">
      <UniqueIdentifier>{a46cbed0-f83f-4d47-8b5f-0f950432c5ea}</UniqueIdentifier>
    </Filter>
    <Filter Include="Quest">
      <UniqueIdentifier>{af87484b-e0bf-48ba-adb2-5c15aa0336a2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Item">
      <UniqueIdentifier>{7ead252b-04d8-4437-8623-9dcd979ddeb3}</UniqueIdentifier>
    </Filter>
    <Filter Include="PathFinder">
      <UniqueIdentifier>{85037920-aa6a-4871-9da7-5001c5c7e74c}</UniqueIdentifier>
    </Filter>
    <Filter Include="NPC">
      <UniqueIdentifier>{c510057f-ac93-48e8-8f41-bd414c3b0cfe}</UniqueIdentifier>
    </Filter>
    <Filter Include="Zone">
      <UniqueIdentifier>{4d590dd8-c210-420d-a5c3-fdfd884cd2ad}</UniqueIdentifier>
    </Filter>
    <Filter Include="PacketQueue">
      <UniqueIdentifier>{ae7ad63b-d664-44ee-96c7-cb8d6ed132c3}</UniqueIdentifier>
    </Filter>
    <Filter Include="PacketProcess">
      <UniqueIdentifier>{eafa2c05-fa61-446c-9bb7-944312a36a74}</UniqueIdentifier>
    </Filter>
    <Filter Include="DNPC_Manager">
      <UniqueIdentifier>{88b2631a-3f9e-48da-88a6-ea8287807c98}</UniqueIdentifier>
    </Filter>
    <Filter Include="DNPC_Manager\DNPC_Script">
      <UniqueIdentifier>{10fa5679-fa01-4b60-9f6f-6ae7bfaba904}</UniqueIdentifier>
    </Filter>
    <Filter Include="LogSvr">
      <UniqueIdentifier>{f802d4ef-e925-421c-b945-3fd8568bbb8e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Scheduler">
      <UniqueIdentifier>{c0753ca4-f04a-4bcc-92ee-cb73ca18832c}</UniqueIdentifier>
    </Filter>
    <Filter Include="User">
      <UniqueIdentifier>{639bb36b-43b4-4b0b-a2fe-458ca41d0baa}</UniqueIdentifier>
    </Filter>
    <Filter Include="User\Community">
      <UniqueIdentifier>{a8ac2aec-c4e2-41db-94dc-425ec3856414}</UniqueIdentifier>
    </Filter>
    <Filter Include="User\Combat">
      <UniqueIdentifier>{ca401a68-2f57-40fd-b0b2-5038d6264934}</UniqueIdentifier>
    </Filter>
    <Filter Include="User\TaxSystem">
      <UniqueIdentifier>{0e99da22-2bc8-404c-9dbf-8238067322cb}</UniqueIdentifier>
    </Filter>
    <Filter Include="User\LTS">
      <UniqueIdentifier>{ec33a4b2-b470-4207-9b1b-b1b19ee90ce4}</UniqueIdentifier>
    </Filter>
    <Filter Include="NDExceptionReport">
      <UniqueIdentifier>{bc256f35-1a0c-4d4d-8570-48e398e4d65b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Server Communication">
      <UniqueIdentifier>{106aa1df-9237-45d9-a641-fb3d24138e4a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Ranking">
      <UniqueIdentifier>{e136aad7-f858-443f-bd06-51a42562a508}</UniqueIdentifier>
    </Filter>
    <Filter Include="Event">
      <UniqueIdentifier>{09c11ca7-f5f6-4870-af9d-182456ea40f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Event\GEvent">
      <UniqueIdentifier>{a54b975c-fe55-42ea-9928-8d86fd239d1b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Event\PKEvent">
      <UniqueIdentifier>{8269123d-fbf4-4008-abfd-1592f216cc94}</UniqueIdentifier>
    </Filter>
    <Filter Include="LogWnd">
      <UniqueIdentifier>{4bf34b67-0ff6-48a1-8bc3-b1b4002036c0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shutdown">
      <UniqueIdentifier>{5205ca70-cbb5-470f-b76a-d2ee8b7637f8}</UniqueIdentifier>
    </Filter>
    <Filter Include="SmallNet">
      <UniqueIdentifier>{fcce6353-0986-414a-93eb-9090fb69b7f9}</UniqueIdentifier>
    </Filter>
    <Filter Include="SmallNet\PktProc">
      <UniqueIdentifier>{39d8f25f-5f47-447c-9879-73716f496613}</UniqueIdentifier>
    </Filter>
    <Filter Include="Acclaim">
      <UniqueIdentifier>{3551d7e7-071c-4433-9436-a8410501b9f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Acclaim\AD">
      <UniqueIdentifier>{c19ab612-2679-4cda-ab27-4bde02336b33}</UniqueIdentifier>
    </Filter>
    <Filter Include="GameGuard">
      <UniqueIdentifier>{24ef4f9e-63fa-4750-9e5f-4c8576ac57ef}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers">
      <UniqueIdentifier>{ae99f960-b56d-4006-a80d-7ac5c8941ed3}</UniqueIdentifier>
    </Filter>
    <Filter Include="GameGuard_New0228">
      <UniqueIdentifier>{abe8cdce-6cf6-4dbb-a0ff-fdf0a1fbe401}</UniqueIdentifier>
    </Filter>
    <Filter Include="Apex">
      <UniqueIdentifier>{a329c52c-b480-48dd-b604-1342589f4ca0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Global\Billing_Define.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\ckSingleton.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\ComTcp_packets.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\CriticalSection.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\eod_base_file.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\FileInput.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\FileLog.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\gDefine.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\gGlobal.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\Global_Define.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\Global_Function.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\NDThread.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Global\NDUtilClass.h">
      <Filter>Global</Filter>
    </ClInclude>
    <ClInclude Include="Packets\Game_packets.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\Packet_for_Battle.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_combat.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_Common.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_ctrl_gms.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\Packet_for_Event.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_ex_battle_room.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_GGAuth.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\Packet_for_GMS.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_items_trade.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\Packet_for_ItemShop.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_Level.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_LifeEtc.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_lobby.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_login.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_nic.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_organization.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_party.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_quest.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_skill.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_svrmove.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="Packets\packet_for_Zone_Monster.h">
      <Filter>Packets</Filter>
    </ClInclude>
    <ClInclude Include="VirtualMemory\VMManager.h">
      <Filter>Virtual Memory</Filter>
    </ClInclude>
    <ClInclude Include="Network\Network.h">
      <Filter>Network</Filter>
    </ClInclude>
    <ClInclude Include="Crypto\crypto.h">
      <Filter>Crypto</Filter>
    </ClInclude>
    <ClInclude Include="Environment\Environment_Read.h">
      <Filter>Environment</Filter>
    </ClInclude>
    <ClInclude Include="Level_Luck\LevelManager.h">
      <Filter>Level_Luck</Filter>
    </ClInclude>
    <ClInclude Include="Skill\SkillManager.h">
      <Filter>Skill</Filter>
    </ClInclude>
    <ClInclude Include="Quest\Quest_Define.h">
      <Filter>Quest</Filter>
    </ClInclude>
    <ClInclude Include="Quest\ServerQuest.h">
      <Filter>Quest</Filter>
    </ClInclude>
    <ClInclude Include="Quest\user_quest.h">
      <Filter>Quest</Filter>
    </ClInclude>
    <ClInclude Include="Item\Item.h">
      <Filter>Item</Filter>
    </ClInclude>
    <ClInclude Include="Item\Item_Manager.h">
      <Filter>Item</Filter>
    </ClInclude>
    <ClInclude Include="Item\PkgMgr.h">
      <Filter>Item</Filter>
    </ClInclude>
    <ClInclude Include="Item\ScriptParser.h">
      <Filter>Item</Filter>
    </ClInclude>
    <ClInclude Include="PathFinder\PathFinder.h">
      <Filter>PathFinder</Filter>
    </ClInclude>
    <ClInclude Include="NPC\CollectNPC.h">
      <Filter>NPC</Filter>
    </ClInclude>
    <ClInclude Include="NPC\NPCBase.h">
      <Filter>NPC</Filter>
    </ClInclude>
    <ClInclude Include="NPC\SNpcManager.h">
      <Filter>NPC</Filter>
    </ClInclude>
    <ClInclude Include="NPC\StaticNpc.h">
      <Filter>NPC</Filter>
    </ClInclude>
    <ClInclude Include="Zone\Zone.h">
      <Filter>Zone</Filter>
    </ClInclude>
    <ClInclude Include="PacketQueue\Packet_Queue.h">
      <Filter>PacketQueue</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckAllocedPool.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPC.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPCAllocedPool.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPCMacro.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPCMessage.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPCMessageQueue.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPCPathManager.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPCPool.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPCProcThread.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckDNPCSpecies.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="DNPC_Manager\ckSpawnInfo.h">
      <Filter>DNPC_Manager</Filter>
    </ClInclude>
    <ClInclude Include="LogSvr\Global_Log_Define.h">
      <Filter>LogSvr</Filter>
    </ClInclude>
    <ClInclude Include="LogSvr\Global_Log_Packet.h">
      <Filter>LogSvr</Filter>
    </ClInclude>
    <ClInclude Include="LogSvr\LogSvrLink.h">
      <Filter>LogSvr</Filter>
    </ClInclude>
    <ClInclude Include="Scheduler\Scheduler.h">
      <Filter>Scheduler</Filter>
    </ClInclude>
    <ClInclude Include="User\ExceptionBattle.h">
      <Filter>User\Community</Filter>
    </ClInclude>
    <ClInclude Include="User\NDParty.h">
      <Filter>User\Community</Filter>
    </ClInclude>
    <ClInclude Include="User\Organization.h">
      <Filter>User\Community</Filter>
    </ClInclude>
    <ClInclude Include="User\Combat.h">
      <Filter>User\Combat</Filter>
    </ClInclude>
    <ClInclude Include="User\CombatModule.h">
      <Filter>User\Combat</Filter>
    </ClInclude>
    <ClInclude Include="User\CombatProc.h">
      <Filter>User\Combat</Filter>
    </ClInclude>
    <ClInclude Include="User\CombatRecord.h">
      <Filter>User\Combat</Filter>
    </ClInclude>
    <ClInclude Include="User\CombatStruct.h">
      <Filter>User\Combat</Filter>
    </ClInclude>
    <ClInclude Include="User\TaxSystem.h">
      <Filter>User\TaxSystem</Filter>
    </ClInclude>
    <ClInclude Include="User\Location_Tracking_System.h">
      <Filter>User\LTS</Filter>
    </ClInclude>
    <ClInclude Include="User\LocationStruct.h">
      <Filter>User\LTS</Filter>
    </ClInclude>
    <ClInclude Include="User\MatchSystem.h">
      <Filter>User</Filter>
    </ClInclude>
    <ClInclude Include="User\User.h">
      <Filter>User</Filter>
    </ClInclude>
    <ClInclude Include="User\UserSkill.h">
      <Filter>User</Filter>
    </ClInclude>
    <ClInclude Include="User\UserTimer.h">
      <Filter>User</Filter>
    </ClInclude>
    <ClInclude Include="NDExceptionReport\NDExceptionReport.h">
      <Filter>NDExceptionReport</Filter>
    </ClInclude>
    <ClInclude Include="Server Communication\Server_Com.h">
      <Filter>Server Communication</Filter>
    </ClInclude>
    <ClInclude Include="Ranking\Ranking.h">
      <Filter>Ranking</Filter>
    </ClInclude>
    <ClInclude Include="Event\GEvent\GameEvent.h">
      <Filter>Event\GEvent</Filter>
    </ClInclude>
    <ClInclude Include="Event\GEvent\GameEventParser.h">
      <Filter>Event\GEvent</Filter>
    </ClInclude>
    <ClInclude Include="Event\GEvent\GEventStructure.h">
      <Filter>Event\GEvent</Filter>
    </ClInclude>
    <ClInclude Include="Event\PKEvent\PkEvent.h">
      <Filter>Event\PKEvent</Filter>
    </ClInclude>
    <ClInclude Include="Event\PKEvent\PkEventScriptParser.h">
      <Filter>Event\PKEvent</Filter>
    </ClInclude>
    <ClInclude Include="LogWnd\LogWnd.h">
      <Filter>LogWnd</Filter>
    </ClInclude>
    <ClInclude Include="Shutdown\FatiguePenaltyListParser.h">
      <Filter>Shutdown</Filter>
    </ClInclude>
    <ClInclude Include="SmallNet\PktProc\_PktDef.h">
      <Filter>SmallNet\PktProc</Filter>
    </ClInclude>
    <ClInclude Include="SmallNet\PktProc\Echo.h">
      <Filter>SmallNet\PktProc</Filter>
    </ClInclude>
    <ClInclude Include="SmallNet\PktProc\PktBilling.h">
      <Filter>SmallNet\PktProc</Filter>
    </ClInclude>
    <ClInclude Include="SmallNet\Packet.h">
      <Filter>SmallNet</Filter>
    </ClInclude>
    <ClInclude Include="SmallNet\PktMgr.h">
      <Filter>SmallNet</Filter>
    </ClInclude>
    <ClInclude Include="SmallNet\PktQue.h">
      <Filter>SmallNet</Filter>
    </ClInclude>
    <ClInclude Include="SmallNet\SmallNet.h">
      <Filter>SmallNet</Filter>
    </ClInclude>
    <ClInclude Include="SmallNet\Sock.h">
      <Filter>SmallNet</Filter>
    </ClInclude>
    <ClInclude Include="Acclaim\AD\Acclaim_AD_RewardListParser.h">
      <Filter>Acclaim\AD</Filter>
    </ClInclude>
    <ClInclude Include="GameGuard\ggsrv25.h">
      <Filter>GameGuard</Filter>
    </ClInclude>
    <ClInclude Include="hdr_acclaim.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="hdr_japan.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="hdr_metel.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="hdr_nexon.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="hdr_nx_innertest.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="hdr_open_test.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="hdr_ot_inner.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="hdr_russia.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="hdr_vina.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="GameGuard_New0228\ggsrv25.h">
      <Filter>GameGuard_New0228</Filter>
    </ClInclude>
    <ClInclude Include="Apex\ApexProxy.h">
      <Filter>Apex</Filter>
    </ClInclude>
    <ClInclude Include="Apex\NDApexFunction.h">
      <Filter>Apex</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Global\eod_base_file.cpp">
      <Filter>Global</Filter>
    </ClCompile>
    <ClCompile Include="Global\FileInput.cpp">
      <Filter>Global</Filter>
    </ClCompile>
    <ClCompile Include="Global\Global_Function.cpp">
      <Filter>Global</Filter>
    </ClCompile>
    <ClCompile Include="Global\NDThread.cpp">
      <Filter>Global</Filter>
    </ClCompile>
    <ClCompile Include="Global\NDUtilClass.cpp">
      <Filter>Global</Filter>
    </ClCompile>
    <ClCompile Include="VirtualMemory\VMManager.cpp">
      <Filter>Virtual Memory</Filter>
    </ClCompile>
    <ClCompile Include="Network\Network.cpp">
      <Filter>Network</Filter>
    </ClCompile>
    <ClCompile Include="Environment\Environment_Read.cpp">
      <Filter>Environment</Filter>
    </ClCompile>
    <ClCompile Include="Level_Luck\LevelManager.cpp">
      <Filter>Level_Luck</Filter>
    </ClCompile>
    <ClCompile Include="Skill\SkillManager.cpp">
      <Filter>Skill</Filter>
    </ClCompile>
    <ClCompile Include="Quest\ServerQuest.cpp">
      <Filter>Quest</Filter>
    </ClCompile>
    <ClCompile Include="Quest\user_quest.cpp">
      <Filter>Quest</Filter>
    </ClCompile>
    <ClCompile Include="Quest\user_quest2.cpp">
      <Filter>Quest</Filter>
    </ClCompile>
    <ClCompile Include="Item\Item_Manager.cpp">
      <Filter>Item</Filter>
    </ClCompile>
    <ClCompile Include="Item\PkgMgr.cpp">
      <Filter>Item</Filter>
    </ClCompile>
    <ClCompile Include="Item\ScriptParser_ItemShop.cpp">
      <Filter>Item</Filter>
    </ClCompile>
    <ClCompile Include="PathFinder\PathFinder.cpp">
      <Filter>PathFinder</Filter>
    </ClCompile>
    <ClCompile Include="NPC\CollectNPC.cpp">
      <Filter>NPC</Filter>
    </ClCompile>
    <ClCompile Include="NPC\SNpcManager.cpp">
      <Filter>NPC</Filter>
    </ClCompile>
    <ClCompile Include="NPC\StaticNpc.cpp">
      <Filter>NPC</Filter>
    </ClCompile>
    <ClCompile Include="Zone\Zone.cpp">
      <Filter>Zone</Filter>
    </ClCompile>
    <ClCompile Include="PacketQueue\Packet_Queue.cpp">
      <Filter>PacketQueue</Filter>
    </ClCompile>
    <ClCompile Include="PacketProcess\PING_Thread.cpp">
      <Filter>PacketProcess</Filter>
    </ClCompile>
    <ClCompile Include="PacketProcess\Proc_General.cpp">
      <Filter>PacketProcess</Filter>
    </ClCompile>
    <ClCompile Include="PacketProcess\Proc_Items.cpp">
      <Filter>PacketProcess</Filter>
    </ClCompile>
    <ClCompile Include="PacketProcess\Proc_LevelSkill.cpp">
      <Filter>PacketProcess</Filter>
    </ClCompile>
    <ClCompile Include="PacketProcess\Proc_Lobby.cpp">
      <Filter>PacketProcess</Filter>
    </ClCompile>
    <ClCompile Include="PacketProcess\Proc_Quest.cpp">
      <Filter>PacketProcess</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_00_00_Type_70.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_02_01_Type_101.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_02_02_Type_102.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_02_03_Type_103.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_02_04_Type_104.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_01_Type_105.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_02_Type_106.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_03_Type_107.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_04_Type_108.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_05_Type_109.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_06_Type_110.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_07_Type_111.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_08_Type_112.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_09_Type_113.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_102_Type_206.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_104_Type_208.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_10_Type_114.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_11_Type_115.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_12_Type_116.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_13_Type_117.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_14_Type_118.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_15_Type_119.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_16_Type_120.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_17_Type_121.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_22_Type_126.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_23_Type_127.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_25_Type_129.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_26_Type_130.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_27_Type_131.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_30_Type_134.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_40_Type_144.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_50_Type_154.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_53_Type_157.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_54_Type_158.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_57_Type_161.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_59_Type_163.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_60_Type_164.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_61_Type_165.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_63_Type_167.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_69_Type_173.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_70_Type_174.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_74_Type_178.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_75_Type_179.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_99_Type_203.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_04_Type_219.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_05_Type_220.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_07_Type_222.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_12_Type_227.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_20_Type_235.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_06_01_Type_238.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_06_05_Type_242.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_07_01A_ISTJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_08_01B_ISTJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_09_03_Type_262.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_09_04_Type_263.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_09_12_Type_271.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_09_NewType.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_10_01_Type_273.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_10_02B_ISFJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_11_03A_INFJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_12_03B_INFJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_13_04A_INTJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_14_04B_INTJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_16_05B_ISTP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_18_06B_ISFP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_19_07A_INFP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_20_07B_INFP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_21_08A_INTP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_22_08B_INTP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_23_09A_ESTP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_24_09B_ESTP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_25_10A_ESFP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_26_10B_ESFP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_277_die_help_Human.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_278_Heal.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_279_SP1.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_27_11A_ENFP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_28_11B_ENFP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_29_12A_ENTP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_30_12B_ENTP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_31_13A_ESTJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_33_14A_ESFJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_35_15A_ENFJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_36_15B_ENFJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_37_16A_ENTJ.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_40_Heal_Type.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_42_AI_02_Type.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_51_Raid_Top.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_52_Raid_Middle.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_53_Tolerance.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_70_manor.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_81_Normal.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_82_Key.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_83_Transform.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_84_HP.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_85_Aggressive.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_86_AggressiveDummy.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_90_Object.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_96_Patrol06_Monster.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Bear.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_BloodWolf.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Fox.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Leopard.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Tiger.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Wolf.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_bookhae_field_boss.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_bookhae_field_raid_a.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_bookhae_field_raid_b.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_bookhae_guard_monster.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Patrol01.cpp">
      <Filter>DNPC_Manager\DNPC_Script</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\ckDNPC.cpp">
      <Filter>DNPC_Manager</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\ckDNPC2.cpp">
      <Filter>DNPC_Manager</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\ckDNPCAllocedPool.cpp">
      <Filter>DNPC_Manager</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\ckDNPCMacro.cpp">
      <Filter>DNPC_Manager</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\ckDNPCMessageQueue.cpp">
      <Filter>DNPC_Manager</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\ckDNPCPathManager.cpp">
      <Filter>DNPC_Manager</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\ckDNPCPool.cpp">
      <Filter>DNPC_Manager</Filter>
    </ClCompile>
    <ClCompile Include="DNPC_Manager\ckDNPCProcThread.cpp">
      <Filter>DNPC_Manager</Filter>
    </ClCompile>
    <ClCompile Include="LogSvr\LogSave_Thread.cpp">
      <Filter>LogSvr</Filter>
    </ClCompile>
    <ClCompile Include="LogSvr\LogSvrLink.cpp">
      <Filter>LogSvr</Filter>
    </ClCompile>
    <ClCompile Include="Scheduler\Scheduler.cpp">
      <Filter>Scheduler</Filter>
    </ClCompile>
    <ClCompile Include="User\ExceptionBattle.cpp">
      <Filter>User\Community</Filter>
    </ClCompile>
    <ClCompile Include="User\NDParty.cpp">
      <Filter>User\Community</Filter>
    </ClCompile>
    <ClCompile Include="User\Organization.cpp">
      <Filter>User\Community</Filter>
    </ClCompile>
    <ClCompile Include="User\Combat.cpp">
      <Filter>User\Combat</Filter>
    </ClCompile>
    <ClCompile Include="User\CombatModule.cpp">
      <Filter>User\Combat</Filter>
    </ClCompile>
    <ClCompile Include="User\CombatProc.cpp">
      <Filter>User\Combat</Filter>
    </ClCompile>
    <ClCompile Include="User\CombatRecord.cpp">
      <Filter>User\Combat</Filter>
    </ClCompile>
    <ClCompile Include="User\TaxSystem.cpp">
      <Filter>User\TaxSystem</Filter>
    </ClCompile>
    <ClCompile Include="User\Location_Tracking_System.cpp">
      <Filter>User\LTS</Filter>
    </ClCompile>
    <ClCompile Include="User\MatchSystem.cpp">
      <Filter>User</Filter>
    </ClCompile>
    <ClCompile Include="User\User.cpp">
      <Filter>User</Filter>
    </ClCompile>
    <ClCompile Include="User\User_Battle_Part.cpp">
      <Filter>User</Filter>
    </ClCompile>
    <ClCompile Include="User\User_Effect_Part.cpp">
      <Filter>User</Filter>
    </ClCompile>
    <ClCompile Include="User\User_Item_Part.cpp">
      <Filter>User</Filter>
    </ClCompile>
    <ClCompile Include="User\UserSkill.cpp">
      <Filter>User</Filter>
    </ClCompile>
    <ClCompile Include="User\UserTimer.cpp">
      <Filter>User</Filter>
    </ClCompile>
    <ClCompile Include="NDExceptionReport\NDExceptionReport.cpp">
      <Filter>NDExceptionReport</Filter>
    </ClCompile>
    <ClCompile Include="Server Communication\Server_Com.cpp">
      <Filter>Server Communication</Filter>
    </ClCompile>
    <ClCompile Include="Server Communication\Server_Com_TCP.cpp">
      <Filter>Server Communication</Filter>
    </ClCompile>
    <ClCompile Include="Ranking\Ranking.cpp">
      <Filter>Ranking</Filter>
    </ClCompile>
    <ClCompile Include="Event\GEvent\GameEvent.cpp">
      <Filter>Event\GEvent</Filter>
    </ClCompile>
    <ClCompile Include="Event\GEvent\GameEventParser.cpp">
      <Filter>Event\GEvent</Filter>
    </ClCompile>
    <ClCompile Include="Event\GEvent\GEventStructure.cpp">
      <Filter>Event\GEvent</Filter>
    </ClCompile>
    <ClCompile Include="Event\PKEvent\PkEvent.cpp">
      <Filter>Event\PKEvent</Filter>
    </ClCompile>
    <ClCompile Include="Event\PKEvent\PkEventScriptParser.cpp">
      <Filter>Event\PKEvent</Filter>
    </ClCompile>
    <ClCompile Include="Shutdown\FatiguePenaltyListParser.cpp">
      <Filter>Shutdown</Filter>
    </ClCompile>
    <ClCompile Include="SmallNet\PktProc\_PkHdr.cpp">
      <Filter>SmallNet\PktProc</Filter>
    </ClCompile>
    <ClCompile Include="SmallNet\PktProc\Echo.cpp">
      <Filter>SmallNet\PktProc</Filter>
    </ClCompile>
    <ClCompile Include="SmallNet\PktProc\NetEventProc.cpp">
      <Filter>SmallNet\PktProc</Filter>
    </ClCompile>
    <ClCompile Include="SmallNet\PktProc\PktBilling.cpp">
      <Filter>SmallNet\PktProc</Filter>
    </ClCompile>
    <ClCompile Include="SmallNet\PktMgr.cpp">
      <Filter>SmallNet</Filter>
    </ClCompile>
    <ClCompile Include="SmallNet\PktQue.cpp">
      <Filter>SmallNet</Filter>
    </ClCompile>
    <ClCompile Include="SmallNet\SmallNet.cpp">
      <Filter>SmallNet</Filter>
    </ClCompile>
    <ClCompile Include="SmallNet\Sock.cpp">
      <Filter>SmallNet</Filter>
    </ClCompile>
    <ClCompile Include="Acclaim\AD\Acclaim_AD_RewardListParser.cpp">
      <Filter>Acclaim\AD</Filter>
    </ClCompile>
    <ClCompile Include="GameGuard\ggsrv25.cpp">
      <Filter>GameGuard</Filter>
    </ClCompile>
    <ClCompile Include="GameGuard_New0228\ggsrv25_2.cpp">
      <Filter>GameGuard_New0228</Filter>
    </ClCompile>
    <ClCompile Include="Apex\ApexProxy.cpp">
      <Filter>Apex</Filter>
    </ClCompile>
    <ClCompile Include="Apex\NDApexFunction.cpp">
      <Filter>Apex</Filter>
    </ClCompile>
    <ClCompile Include="NineDragonsServer.cpp" />
    <ClCompile Include="restore_contents.cpp" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="LogWnd\mLogWnd.lib">
      <Filter>LogWnd</Filter>
    </Library>
    <Library Include="LogWnd\uLogWnd.lib">
      <Filter>LogWnd</Filter>
    </Library>
    <Library Include="mFileLog.lib" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="Shutdown\FatiguePenaltyList.txt">
      <Filter>Shutdown</Filter>
    </Text>
    <Text Include="nfofile\Acclaim_AD_RewardList.txt">
      <Filter>Acclaim\AD</Filter>
    </Text>
    <Text Include="ReadMe.txt" />
    <Text Include="Temp.txt" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="Crypto\lump.dat">
      <Filter>Crypto</Filter>
    </CustomBuild>
    <CustomBuild Include="mFileLog.dll" />
  </ItemGroup>
</Project>