﻿C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\Microsoft.CppBuild.targets(296,5): warning MSB8003: 未能在注册表中找到 VCInstallDir 变量。TargetFrameworkVersion 或 PlatformToolset 可能设置为无效的版本号。
C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\Platforms\Win32\PlatformToolsets\v70\Microsoft.Cpp.Win32.v70.targets(307,5): error MSB4062: 未能从程序集 C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\Platforms\Win32\PlatformToolsets\v70\Daffodil.CPPTasks.Win32.v70.dll 加载任务“CLA”。Could not load file or assembly 'file:///C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\Platforms\Win32\PlatformToolsets\v70\Daffodil.CPPTasks.Win32.v70.dll' or one of its dependencies. 试图加载格式不正确的程序。 请确认 <UsingTask> 声明正确，该程序集及其所有依赖项都可用，并且该任务包含实现 Microsoft.Build.Framework.ITask 的公共类。
