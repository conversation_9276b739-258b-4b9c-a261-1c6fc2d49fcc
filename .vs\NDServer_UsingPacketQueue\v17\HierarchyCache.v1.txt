﻿++解决方案 'NDServer_UsingPacketQueue' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:NDServer_UsingPacketQueue.sln
++NDServer_UsingPacketQueue ‎(Visual Studio 2019)
i:{00000000-0000-0000-0000-000000000000}:NDServer_UsingPacketQueue
++引用
i:{a5ef0e96-fe77-40db-8679-************}:{F1AF8102-DE94-4A13-9DB5-70B403A22B29}
++外部依赖项
i:{a5ef0e96-fe77-40db-8679-************}:{46DCA12D-132B-4CEF-9001-64BD85716F08}
++Acclaim
i:{a5ef0e96-fe77-40db-8679-************}:Acclaim
++Apex
i:{a5ef0e96-fe77-40db-8679-************}:Apex
++Crypto
i:{a5ef0e96-fe77-40db-8679-************}:Crypto
++DNPC_Manager
i:{a5ef0e96-fe77-40db-8679-************}:DNPC_Manager
++Environment
i:{a5ef0e96-fe77-40db-8679-************}:Environment
++Event
i:{a5ef0e96-fe77-40db-8679-************}:Event
++GameGuard
i:{a5ef0e96-fe77-40db-8679-************}:GameGuard
++ggsrv25.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\GameGuard\ggsrv25.cpp
++ggsrv25.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\GameGuard\ggsrv25.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\GameGuard_New0228\ggsrv25.h
++GameGuard_New0228
i:{a5ef0e96-fe77-40db-8679-************}:GameGuard_New0228
++Global
i:{a5ef0e96-fe77-40db-8679-************}:Global
++Billing_Define.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\Billing_Define.h
++ckSingleton.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\ckSingleton.h
++ComTcp_packets.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\ComTcp_packets.h
++CriticalSection.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\CriticalSection.h
++eod_base_file.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\eod_base_file.cpp
++eod_base_file.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\eod_base_file.h
++FileInput.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\FileInput.cpp
++FileInput.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\FileInput.h
++FileLog.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\FileLog.h
++gDefine.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\gDefine.h
++gGlobal.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\gGlobal.h
++Global_Define.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\Global_Define.h
++Global_Function.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\Global_Function.cpp
++Global_Function.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\Global_Function.h
++NDThread.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\NDThread.cpp
++NDThread.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\NDThread.h
++NDUtilClass.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\NDUtilClass.cpp
++NDUtilClass.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Global\NDUtilClass.h
++Headers
i:{a5ef0e96-fe77-40db-8679-************}:Headers
++Item
i:{a5ef0e96-fe77-40db-8679-************}:Item
++Item.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Item\Item.h
++Item_Manager.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Item\Item_Manager.cpp
++Item_Manager.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Item\Item_Manager.h
++PkgMgr.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Item\PkgMgr.cpp
++PkgMgr.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Item\PkgMgr.h
++ScriptParser.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Item\ScriptParser.h
++ScriptParser_ItemShop.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Item\ScriptParser_ItemShop.cpp
++Level_Luck
i:{a5ef0e96-fe77-40db-8679-************}:Level_Luck
++LogSvr
i:{a5ef0e96-fe77-40db-8679-************}:LogSvr
++LogWnd
i:{a5ef0e96-fe77-40db-8679-************}:LogWnd
++NDExceptionReport
i:{a5ef0e96-fe77-40db-8679-************}:NDExceptionReport
++Network
i:{a5ef0e96-fe77-40db-8679-************}:Network
++NPC
i:{a5ef0e96-fe77-40db-8679-************}:NPC
++PacketProcess
i:{a5ef0e96-fe77-40db-8679-************}:PacketProcess
++PacketQueue
i:{a5ef0e96-fe77-40db-8679-************}:PacketQueue
++Packets
i:{a5ef0e96-fe77-40db-8679-************}:Packets
++PathFinder
i:{a5ef0e96-fe77-40db-8679-************}:PathFinder
++Quest
i:{a5ef0e96-fe77-40db-8679-************}:Quest
++Ranking
i:{a5ef0e96-fe77-40db-8679-************}:Ranking
++Scheduler
i:{a5ef0e96-fe77-40db-8679-************}:Scheduler
++Server Communication
i:{a5ef0e96-fe77-40db-8679-************}:Server Communication
++Shutdown
i:{a5ef0e96-fe77-40db-8679-************}:Shutdown
++Skill
i:{a5ef0e96-fe77-40db-8679-************}:Skill
++SmallNet
i:{a5ef0e96-fe77-40db-8679-************}:SmallNet
++User
i:{a5ef0e96-fe77-40db-8679-************}:User
++Virtual Memory
i:{a5ef0e96-fe77-40db-8679-************}:Virtual Memory
++Zone
i:{a5ef0e96-fe77-40db-8679-************}:Zone
++mFileLog.dll
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\mFileLog.dll
++mFileLog.lib
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\mFileLog.lib
++NineDragonsServer.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NineDragonsServer.cpp
++ReadMe.txt
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\ReadMe.txt
++restore_contents.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\restore_contents.cpp
++Temp.txt
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Temp.txt
++__msvc_bit_utils.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_BIT_UTILS.HPP
++__msvc_filebuf.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_FILEBUF.HPP
++__msvc_format_ucd_tables.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_FORMAT_UCD_TABLES.HPP
++__msvc_int128.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_INT128.HPP
++__msvc_iter_core.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_ITER_CORE.HPP
++__msvc_print.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_PRINT.HPP
++__msvc_sanitizer_annotate_container.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_SANITIZER_ANNOTATE_CONTAINER.HPP
++__msvc_system_error_abi.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_SYSTEM_ERROR_ABI.HPP
++__msvc_xlocinfo_types.hpp
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\__MSVC_XLOCINFO_TYPES.HPP
++algorithm
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ALGORITHM
++ammintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\AMMINTRIN.H
++apiquery2.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\APIQUERY2.H
++apiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\APISET.H
++apisetcconv.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\APISETCCONV.H
++arm_neon.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ARM_NEON.H
++arm64_neon.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ARM64_NEON.H
++arm64intr.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ARM64INTR.H
++armintr.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ARMINTR.H
++array
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ARRAY
++assert.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\ASSERT.H
++atomic
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ATOMIC
++basetsd.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\BASETSD.H
++bcrypt.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\BCRYPT.H
++bit
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\BIT
++cassert
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CASSERT
++cctype
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CCTYPE
++cderr.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\CDERR.H
++cerrno
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CERRNO
++cfloat
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CFLOAT
++cguid.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\CGUID.H
++charconv
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CHARCONV
++climits
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CLIMITS
++clocale
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CLOCALE
++cmath
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CMATH
++combaseapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\COMBASEAPI.H
++coml2api.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\COML2API.H
++commctrl.rh
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\COMMCTRL.RH
++commdlg.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\COMMDLG.H
++commdlg.inl
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\COMMDLG.INL
++compare
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\COMPARE
++concepts
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CONCEPTS
++concurrencysal.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CONCURRENCYSAL.H
++consoleapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\CONSOLEAPI.H
++consoleapi2.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\CONSOLEAPI2.H
++consoleapi3.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\CONSOLEAPI3.H
++corecrt.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT.H
++corecrt_malloc.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_MALLOC.H
++corecrt_math.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_MATH.H
++corecrt_math_defines.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_MATH_DEFINES.H
++corecrt_memcpy_s.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_MEMCPY_S.H
++corecrt_memory.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_MEMORY.H
++corecrt_search.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_SEARCH.H
++corecrt_share.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_SHARE.H
++corecrt_startup.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_STARTUP.H
++corecrt_stdio_config.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_STDIO_CONFIG.H
++corecrt_terminate.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_TERMINATE.H
++corecrt_wconio.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WCONIO.H
++corecrt_wctype.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WCTYPE.H
++corecrt_wdirect.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WDIRECT.H
++corecrt_wio.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WIO.H
++corecrt_wprocess.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WPROCESS.H
++corecrt_wstdio.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WSTDIO.H
++corecrt_wstdlib.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WSTDLIB.H
++corecrt_wstring.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WSTRING.H
++corecrt_wtime.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CORECRT_WTIME.H
++crtdbg.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CRTDBG.H
++crtdefs.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CRTDEFS.H
++cstddef
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CSTDDEF
++cstdint
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CSTDINT
++cstdio
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CSTDIO
++cstdlib
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CSTDLIB
++cstring
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CSTRING
++ctime
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CTIME
++ctype.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\CTYPE.H
++cwchar
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CWCHAR
++datetimeapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\DATETIMEAPI.H
++DbgHelp.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\DBGHELP.H
++dde.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\DDE.H
++dde.rh
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\DDE.RH
++ddeml.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\DDEML.H
++debugapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\DEBUGAPI.H
++deque
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\DEQUE
++devpkey.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\DEVPKEY.H
++devpropdef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\DEVPROPDEF.H
++dlgs.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\DLGS.H
++dpapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\DPAPI.H
++driverspecs.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\DRIVERSPECS.H
++eh.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\EH.H
++emmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\EMMINTRIN.H
++enclaveapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\ENCLAVEAPI.H
++errhandlingapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\ERRHANDLINGAPI.H
++errno.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\ERRNO.H
++exception
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\EXCEPTION
++excpt.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\EXCPT.H
++fibersapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\FIBERSAPI.H
++fileapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\FILEAPI.H
++fileapifromapp.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\FILEAPIFROMAPP.H
++float.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\FLOAT.H
++format
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\FORMAT
++fstream
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\FSTREAM
++guiddef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\GUIDDEF.H
++handleapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\HANDLEAPI.H
++hashtypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\HASHTYPES.H
++heapapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\HEAPAPI.H
++ifdef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\IFDEF.H
++ifmib.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\IFMIB.H
++ime_cmodes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\IME_CMODES.H
++imm.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\IMM.H
++immintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\IMMINTRIN.H
++in6addr.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\IN6ADDR.H
++inaddr.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\INADDR.H
++initializer_list
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\INITIALIZER_LIST
++interlockedapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\INTERLOCKEDAPI.H
++intrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\INTRIN.H
++intrin0.inl.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\INTRIN0.INL.H
++ioapiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\IOAPISET.H
++ioctltypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\IOCTLTYPES.H
++ios
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\IOS
++iosfwd
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\IOSFWD
++iostream
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\IOSTREAM
++IPExport.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\IPEXPORT.H
++iphlpapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\IPHLPAPI.H
++ipifcons.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\IPIFCONS.H
++ipmib.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\IPMIB.H
++Iprtrmib.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\IPRTRMIB.H
++IPTypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\IPTYPES.H
++ipv6prefast.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\IPV6PREFAST.H
++istream
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ISTREAM
++iterator
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ITERATOR
++jobapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\JOBAPI.H
++jobapi2.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\JOBAPI2.H
++joystickapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\JOYSTICKAPI.H
++kernelspecs.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\KERNELSPECS.H
++ktmtypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\KTMTYPES.H
++libloaderapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\LIBLOADERAPI.H
++limits
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\LIMITS
++limits.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\LIMITS.H
++list
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\LIST
++lmcons.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\LMCONS.H
++locale
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\LOCALE
++locale.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\LOCALE.H
++lzexpand.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\LZEXPAND.H
++malloc.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\MALLOC.H
++map
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\MAP
++math.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\MATH.H
++mbstring.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\MBSTRING.H
++mciapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MCIAPI.H
++mcx.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MCX.H
++memory
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\MEMORY
++memoryapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MEMORYAPI.H
++minidumpapiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MINIDUMPAPISET.H
++minwinbase.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MINWINBASE.H
++minwindef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\MINWINDEF.H
++mm3dnow.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\MM3DNOW.H
++mmeapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MMEAPI.H
++mmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\MMINTRIN.H
++mmiscapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MMISCAPI.H
++mmiscapi2.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MMISCAPI2.H
++mmsyscom.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MMSYSCOM.H
++mmsystem.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MMSYSTEM.H
++mprapidef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\MPRAPIDEF.H
++MSWSock.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MSWSOCK.H
++mswsockdef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\MSWSOCKDEF.H
++msxml.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\MSXML.H
++namedpipeapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\NAMEDPIPEAPI.H
++namespaceapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\NAMESPACEAPI.H
++nb30.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\NB30.H
++ncrypt.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\NCRYPT.H
++ndisport.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\NDISPORT.H
++ndkinfo.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDKINFO.H
++netioapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NETIOAPI.H
++new
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\NEW
++nicswitchtypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\NICSWITCHTYPES.H
++nldef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NLDEF.H
++nmmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\NMMINTRIN.H
++no_sal2.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NO_SAL2.H
++ntddndis.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NTDDNDIS.H
++oaidl.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\OAIDL.H
++objbase.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\OBJBASE.H
++objectheader.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\OBJECTHEADER.H
++objidl.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\OBJIDL.H
++objidlbase.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\OBJIDLBASE.H
++offloadtypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\OFFLOADTYPES.H
++oidtypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\OIDTYPES.H
++ole.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\OLE.H
++ole2.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\OLE2.H
++oleauto.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\OLEAUTO.H
++oleidl.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\OLEIDL.H
++optional
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\OPTIONAL
++ostream
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\OSTREAM
++pciprop.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\PCIPROP.H
++playsoundapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PLAYSOUNDAPI.H
++pmmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\PMMINTRIN.H
++poppack.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\POPPACK.H
++process.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\PROCESS.H
++processenv.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PROCESSENV.H
++processthreadsapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PROCESSTHREADSAPI.H
++processtopologyapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PROCESSTOPOLOGYAPI.H
++profileapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PROFILEAPI.H
++PropIdl.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PROPIDL.H
++PropIdlBase.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PROPIDLBASE.H
++prsht.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PRSHT.H
++prsht.inl
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\PRSHT.INL
++pshpack1.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\PSHPACK1.H
++pshpack2.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\PSHPACK2.H
++pshpack4.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\PSHPACK4.H
++pshpack8.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\PSHPACK8.H
++qos.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\QOS.H
++queue
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\QUEUE
++ranges
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\RANGES
++realtimeapiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\REALTIMEAPISET.H
++reason.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\REASON.H
++rpc.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\RPC.H
++rpcasync.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\RPCASYNC.H
++rpcdce.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\RPCDCE.H
++rpcdcep.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\RPCDCEP.H
++rpcndr.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\RPCNDR.H
++rpcnsi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\RPCNSI.H
++rpcnsip.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\RPCNSIP.H
++rpcnterr.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\RPCNTERR.H
++rpcsal.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\RPCSAL.H
++sal.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\SAL.H
++SCardErr.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SCARDERR.H
++sdkddkver.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\SDKDDKVER.H
++sdv_driverspecs.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\SDV_DRIVERSPECS.H
++securityappcontainer.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SECURITYAPPCONTAINER.H
++securitybaseapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SECURITYBASEAPI.H
++servprov.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SERVPROV.H
++set
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\SET
++setjmp.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\SETJMP.H
++setjmpex.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\SETJMPEX.H
++share.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\SHARE.H
++shellapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SHELLAPI.H
++smmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\SMMINTRIN.H
++softintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SOFTINTRIN.H
++sourceannotations.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\CODEANALYSIS\SOURCEANNOTATIONS.H
++span
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\SPAN
++specstrings.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\SPECSTRINGS.H
++specstrings_strict.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\SPECSTRINGS_STRICT.H
++specstrings_undef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\SPECSTRINGS_UNDEF.H
++sql.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SQL.H
++sqlext.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SQLEXT.H
++sqltypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SQLTYPES.H
++sqlucode.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SQLUCODE.H
++stat.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\SYS\STAT.H
++stdarg.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\STDARG.H
++stddef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\STDDEF.H
++stdexcept
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\STDEXCEPT
++stdint.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\STDINT.H
++stdio.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\STDIO.H
++stdlib.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\STDLIB.H
++stralign.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\STRALIGN.H
++streambuf
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\STREAMBUF
++string
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\STRING
++string.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\STRING.H
++string_view
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\STRING_VIEW
++stringapiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\STRINGAPISET.H
++synchapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SYNCHAPI.H
++sysinfoapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SYSINFOAPI.H
++system_error
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\SYSTEM_ERROR
++systemtopologyapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\SYSTEMTOPOLOGYAPI.H
++tchar.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\TCHAR.H
++tcpestats.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\TCPESTATS.H
++tcpmib.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\TCPMIB.H
++threadpoolapiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\THREADPOOLAPISET.H
++threadpoollegacyapiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\THREADPOOLLEGACYAPISET.H
++time.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\TIME.H
++timeapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\TIMEAPI.H
++timeb.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\SYS\TIMEB.H
++timezoneapi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\TIMEZONEAPI.H
++tmmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\TMMINTRIN.H
++tuple
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\TUPLE
++tvout.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\TVOUT.H
++type_traits
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\TYPE_TRAITS
++typeinfo
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\TYPEINFO
++types.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\SYS\TYPES.H
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\TYPES.H
++udpmib.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\UDPMIB.H
++Unknwn.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\UNKNWN.H
++Unknwnbase.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\UNKNWNBASE.H
++urlmon.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\URLMON.H
++use_ansi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\USE_ANSI.H
++utilapiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\UTILAPISET.H
++utility
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\UTILITY
++vadefs.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VADEFS.H
++vcruntime.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VCRUNTIME.H
++vcruntime_exception.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VCRUNTIME_EXCEPTION.H
++vcruntime_new.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VCRUNTIME_NEW.H
++vcruntime_new_debug.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VCRUNTIME_NEW_DEBUG.H
++vcruntime_startup.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VCRUNTIME_STARTUP.H
++vcruntime_string.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VCRUNTIME_STRING.H
++vcruntime_typeinfo.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VCRUNTIME_TYPEINFO.H
++vector
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\VECTOR
++verrsrc.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\VERRSRC.H
++version.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\NDIS\VERSION.H
++wchar.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UCRT\WCHAR.H
++widemath.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WIDEMATH.H
++winapifamily.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WINAPIFAMILY.H
++WinBase.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINBASE.H
++winbase.inl
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINBASE.INL
++wincon.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINCON.H
++wincontypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINCONTYPES.H
++wincrypt.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINCRYPT.H
++windef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WINDEF.H
++windot11.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WINDOT11.H
++Windows.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINDOWS.H
++winefs.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINEFS.H
++winerror.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WINERROR.H
++wingdi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINGDI.H
++winioctl.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINIOCTL.H
++winnetwk.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINNETWK.H
++WinNls.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINNLS.H
++winnt.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINNT.H
++winnt.rh
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINNT.RH
++winpackagefamily.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WINPACKAGEFAMILY.H
++winperf.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINPERF.H
++winreg.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINREG.H
++winresrc.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINRESRC.H
++winscard.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINSCARD.H
++winsmcrd.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WINSMCRD.H
++winsock.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINSOCK.H
++WinSock2.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINSOCK2.H
++winspool.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINSPOOL.H
++winsvc.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINSVC.H
++WinUser.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINUSER.H
++winuser.inl
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINUSER.INL
++winuser.rh
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINUSER.RH
++winver.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WINVER.H
++wlantypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WLANTYPES.H
++wmmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\WMMINTRIN.H
++wnnc.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WNNC.H
++wow64apiset.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WOW64APISET.H
++ws2def.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WS2DEF.H
++ws2ipdef.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WS2IPDEF.H
++WS2tcpip.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WS2TCPIP.H
++wsipv6ok.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WSIPV6OK.H
++WSPiApi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\UM\WSPIAPI.H
++wtypes.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WTYPES.H
++WTypesbase.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES (X86)\WINDOWS KITS\10\INCLUDE\10.0.22621.0\SHARED\WTYPESBASE.H
++xatomic.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XATOMIC.H
++xatomic_wait.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XATOMIC_WAIT.H
++xbit_ops.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XBIT_OPS.H
++xcall_once.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XCALL_ONCE.H
++xcharconv.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XCHARCONV.H
++xcharconv_ryu.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XCHARCONV_RYU.H
++xcharconv_ryu_tables.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XCHARCONV_RYU_TABLES.H
++xcharconv_tables.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XCHARCONV_TABLES.H
++xerrc.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XERRC.H
++xfacet
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XFACET
++xfilesystem_abi.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XFILESYSTEM_ABI.H
++xiosbase
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XIOSBASE
++xkeycheck.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XKEYCHECK.H
++xlocale
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XLOCALE
++xlocbuf
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XLOCBUF
++xlocinfo
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XLOCINFO
++xlocmes
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XLOCMES
++xlocmon
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XLOCMON
++xlocnum
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XLOCNUM
++xloctime
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XLOCTIME
++xmemory
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XMEMORY
++xmmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XMMINTRIN.H
++xnode_handle.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XNODE_HANDLE.H
++xpolymorphic_allocator.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XPOLYMORPHIC_ALLOCATOR.H
++xsmf_control.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XSMF_CONTROL.H
++xstring
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XSTRING
++xthreads.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XTHREADS.H
++xtimec.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XTIMEC.H
++xtr1common
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XTR1COMMON
++xtree
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XTREE
++xutility
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\XUTILITY
++yvals.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\YVALS.H
++yvals_core.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\YVALS_CORE.H
++zmmintrin.h
e:{a5ef0e96-fe77-40db-8679-************}:C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\ENTERPRISE\VC\TOOLS\MSVC\14.38.33130\INCLUDE\ZMMINTRIN.H
++AD
i:{a5ef0e96-fe77-40db-8679-************}:Acclaim\AD
++ApexProxy.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Apex\ApexProxy.cpp
++ApexProxy.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Apex\ApexProxy.h
++NDApexFunction.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Apex\NDApexFunction.cpp
++NDApexFunction.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Apex\NDApexFunction.h
++crypto.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Crypto\crypto.h
++lump.dat
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Crypto\lump.dat
++DNPC_Script
i:{a5ef0e96-fe77-40db-8679-************}:DNPC_Manager\DNPC_Script
++ckAllocedPool.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckAllocedPool.h
++ckDNPC.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPC.cpp
++ckDNPC.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPC.h
++ckDNPC2.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPC2.cpp
++ckDNPCAllocedPool.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCAllocedPool.cpp
++ckDNPCAllocedPool.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCAllocedPool.h
++ckDNPCMacro.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCMacro.cpp
++ckDNPCMacro.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCMacro.h
++ckDNPCMessage.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCMessage.h
++ckDNPCMessageQueue.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCMessageQueue.cpp
++ckDNPCMessageQueue.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCMessageQueue.h
++ckDNPCPathManager.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCPathManager.cpp
++ckDNPCPathManager.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCPathManager.h
++ckDNPCPool.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCPool.cpp
++ckDNPCPool.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCPool.h
++ckDNPCProcThread.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCProcThread.cpp
++ckDNPCProcThread.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCProcThread.h
++ckDNPCSpecies.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckDNPCSpecies.h
++ckSpawnInfo.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\ckSpawnInfo.h
++Environment_Read.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Environment\Environment_Read.cpp
++Environment_Read.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Environment\Environment_Read.h
++GEvent
i:{a5ef0e96-fe77-40db-8679-************}:Event\GEvent
++PKEvent
i:{a5ef0e96-fe77-40db-8679-************}:Event\PKEvent
++ggsrv25_2.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\GameGuard_New0228\ggsrv25_2.cpp
++hdr_acclaim.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_acclaim.h
++hdr_japan.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_japan.h
++hdr_metel.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_metel.h
++hdr_nexon.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_nexon.h
++hdr_nx_innertest.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_nx_innertest.h
++hdr_open_test.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_open_test.h
++hdr_ot_inner.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_ot_inner.h
++hdr_russia.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_russia.h
++hdr_vina.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\hdr_vina.h
++StdAfx.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\StdAfx.h
++LevelManager.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Level_Luck\LevelManager.cpp
++LevelManager.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Level_Luck\LevelManager.h
++Global_Log_Define.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\LogSvr\Global_Log_Define.h
++Global_Log_Packet.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\LogSvr\Global_Log_Packet.h
++LogSave_Thread.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\LogSvr\LogSave_Thread.cpp
++LogSvrLink.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\LogSvr\LogSvrLink.cpp
++LogSvrLink.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\LogSvr\LogSvrLink.h
++LogWnd.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\LogWnd\LogWnd.h
++mLogWnd.lib
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\LogWnd\mLogWnd.lib
++uLogWnd.lib
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\LogWnd\uLogWnd.lib
++NDExceptionReport.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NDExceptionReport\NDExceptionReport.cpp
++NDExceptionReport.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NDExceptionReport\NDExceptionReport.h
++Network.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Network\Network.cpp
++Network.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Network\Network.h
++CollectNPC.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NPC\CollectNPC.cpp
++CollectNPC.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NPC\CollectNPC.h
++NPCBase.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NPC\NPCBase.h
++SNpcManager.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NPC\SNpcManager.cpp
++SNpcManager.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NPC\SNpcManager.h
++StaticNpc.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NPC\StaticNpc.cpp
++StaticNpc.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\NPC\StaticNpc.h
++PING_Thread.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PacketProcess\PING_Thread.cpp
++Proc_General.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PacketProcess\Proc_General.cpp
++Proc_Items.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PacketProcess\Proc_Items.cpp
++Proc_LevelSkill.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PacketProcess\Proc_LevelSkill.cpp
++Proc_Lobby.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PacketProcess\Proc_Lobby.cpp
++Proc_Quest.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PacketProcess\Proc_Quest.cpp
++Packet_Queue.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PacketQueue\Packet_Queue.cpp
++Packet_Queue.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PacketQueue\Packet_Queue.h
++Game_packets.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\Game_packets.h
++Packet_for_Battle.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\Packet_for_Battle.h
++packet_for_combat.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_combat.h
++packet_for_Common.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_Common.h
++packet_for_ctrl_gms.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_ctrl_gms.h
++Packet_for_Event.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\Packet_for_Event.h
++packet_for_ex_battle_room.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_ex_battle_room.h
++packet_for_GGAuth.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_GGAuth.h
++Packet_for_GMS.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\Packet_for_GMS.h
++packet_for_items_trade.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_items_trade.h
++Packet_for_ItemShop.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\Packet_for_ItemShop.h
++packet_for_Level.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_Level.h
++packet_for_LifeEtc.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_LifeEtc.h
++packet_for_lobby.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_lobby.h
++packet_for_login.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_login.h
++packet_for_nic.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_nic.h
++packet_for_organization.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_organization.h
++packet_for_party.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_party.h
++packet_for_quest.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_quest.h
++packet_for_skill.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_skill.h
++packet_for_svrmove.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_svrmove.h
++packet_for_Zone_Monster.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Packets\packet_for_Zone_Monster.h
++PathFinder.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PathFinder\PathFinder.cpp
++PathFinder.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\PathFinder\PathFinder.h
++Quest_Define.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Quest\Quest_Define.h
++ServerQuest.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Quest\ServerQuest.cpp
++ServerQuest.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Quest\ServerQuest.h
++user_quest.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Quest\user_quest.cpp
++user_quest.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Quest\user_quest.h
++user_quest2.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Quest\user_quest2.cpp
++Ranking.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Ranking\Ranking.cpp
++Ranking.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Ranking\Ranking.h
++Scheduler.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Scheduler\Scheduler.cpp
++Scheduler.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Scheduler\Scheduler.h
++Server_Com.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Server Communication\Server_Com.cpp
++Server_Com.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Server Communication\Server_Com.h
++Server_Com_TCP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Server Communication\Server_Com_TCP.cpp
++FatiguePenaltyList.txt
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Shutdown\FatiguePenaltyList.txt
++FatiguePenaltyListParser.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Shutdown\FatiguePenaltyListParser.cpp
++FatiguePenaltyListParser.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Shutdown\FatiguePenaltyListParser.h
++SkillManager.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Skill\SkillManager.cpp
++SkillManager.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Skill\SkillManager.h
++PktProc
i:{a5ef0e96-fe77-40db-8679-************}:SmallNet\PktProc
++Packet.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\Packet.h
++PktMgr.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktMgr.cpp
++PktMgr.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktMgr.h
++PktQue.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktQue.cpp
++PktQue.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktQue.h
++SmallNet.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\SmallNet.cpp
++SmallNet.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\SmallNet.h
++Sock.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\Sock.cpp
++Sock.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\Sock.h
++Combat
i:{a5ef0e96-fe77-40db-8679-************}:User\Combat
++Community
i:{a5ef0e96-fe77-40db-8679-************}:User\Community
++LTS
i:{a5ef0e96-fe77-40db-8679-************}:User\LTS
++TaxSystem
i:{a5ef0e96-fe77-40db-8679-************}:User\TaxSystem
++MatchSystem.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\MatchSystem.cpp
++MatchSystem.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\MatchSystem.h
++User.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\User.cpp
++User.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\User.h
++User_Battle_Part.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\User_Battle_Part.cpp
++User_Effect_Part.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\User_Effect_Part.cpp
++User_Item_Part.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\User_Item_Part.cpp
++UserSkill.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\UserSkill.cpp
++UserSkill.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\UserSkill.h
++UserTimer.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\UserTimer.cpp
++UserTimer.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\UserTimer.h
++VMManager.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\VirtualMemory\VMManager.cpp
++VMManager.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\VirtualMemory\VMManager.h
++Zone.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Zone\Zone.cpp
++Zone.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Zone\Zone.h
++Acclaim_AD_RewardList.txt
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\nfofile\Acclaim_AD_RewardList.txt
++Acclaim_AD_RewardListParser.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Acclaim\AD\Acclaim_AD_RewardListParser.cpp
++Acclaim_AD_RewardListParser.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Acclaim\AD\Acclaim_AD_RewardListParser.h
++DNPC_00_00_Type_70.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_00_00_Type_70.cpp
++DNPC_02_01_Type_101.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_02_01_Type_101.cpp
++DNPC_02_02_Type_102.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_02_02_Type_102.cpp
++DNPC_02_03_Type_103.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_02_03_Type_103.cpp
++DNPC_02_04_Type_104.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_02_04_Type_104.cpp
++DNPC_03_01_Type_105.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_01_Type_105.cpp
++DNPC_03_02_Type_106.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_02_Type_106.cpp
++DNPC_03_03_Type_107.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_03_Type_107.cpp
++DNPC_03_04_Type_108.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_04_Type_108.cpp
++DNPC_03_05_Type_109.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_05_Type_109.cpp
++DNPC_03_06_Type_110.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_06_Type_110.cpp
++DNPC_03_07_Type_111.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_07_Type_111.cpp
++DNPC_03_08_Type_112.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_08_Type_112.cpp
++DNPC_03_09_Type_113.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_09_Type_113.cpp
++DNPC_03_10_Type_114.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_10_Type_114.cpp
++DNPC_03_11_Type_115.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_11_Type_115.cpp
++DNPC_03_12_Type_116.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_12_Type_116.cpp
++DNPC_03_13_Type_117.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_13_Type_117.cpp
++DNPC_03_14_Type_118.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_14_Type_118.cpp
++DNPC_03_15_Type_119.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_15_Type_119.cpp
++DNPC_03_16_Type_120.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_16_Type_120.cpp
++DNPC_03_17_Type_121.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_17_Type_121.cpp
++DNPC_03_22_Type_126.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_22_Type_126.cpp
++DNPC_03_23_Type_127.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_23_Type_127.cpp
++DNPC_03_25_Type_129.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_25_Type_129.cpp
++DNPC_03_26_Type_130.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_26_Type_130.cpp
++DNPC_03_27_Type_131.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_27_Type_131.cpp
++DNPC_03_30_Type_134.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_30_Type_134.cpp
++DNPC_03_40_Type_144.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_40_Type_144.cpp
++DNPC_03_50_Type_154.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_50_Type_154.cpp
++DNPC_03_53_Type_157.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_53_Type_157.cpp
++DNPC_03_54_Type_158.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_54_Type_158.cpp
++DNPC_03_57_Type_161.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_57_Type_161.cpp
++DNPC_03_59_Type_163.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_59_Type_163.cpp
++DNPC_03_60_Type_164.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_60_Type_164.cpp
++DNPC_03_61_Type_165.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_61_Type_165.cpp
++DNPC_03_63_Type_167.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_63_Type_167.cpp
++DNPC_03_69_Type_173.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_69_Type_173.cpp
++DNPC_03_70_Type_174.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_70_Type_174.cpp
++DNPC_03_74_Type_178.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_74_Type_178.cpp
++DNPC_03_75_Type_179.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_75_Type_179.cpp
++DNPC_03_99_Type_203.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_99_Type_203.cpp
++DNPC_03_102_Type_206.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_102_Type_206.cpp
++DNPC_03_104_Type_208.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_03_104_Type_208.cpp
++DNPC_05_04_Type_219.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_05_04_Type_219.cpp
++DNPC_05_05_Type_220.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_05_05_Type_220.cpp
++DNPC_05_07_Type_222.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_05_07_Type_222.cpp
++DNPC_05_12_Type_227.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_05_12_Type_227.cpp
++DNPC_05_20_Type_235.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_05_20_Type_235.cpp
++DNPC_06_01_Type_238.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_06_01_Type_238.cpp
++DNPC_06_05_Type_242.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_06_05_Type_242.cpp
++DNPC_07_01A_ISTJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_07_01A_ISTJ.cpp
++DNPC_08_01B_ISTJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_08_01B_ISTJ.cpp
++DNPC_09_03_Type_262.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_09_03_Type_262.cpp
++DNPC_09_04_Type_263.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_09_04_Type_263.cpp
++DNPC_09_12_Type_271.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_09_12_Type_271.cpp
++DNPC_09_NewType.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_09_NewType.cpp
++DNPC_10_01_Type_273.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_10_01_Type_273.cpp
++DNPC_10_02B_ISFJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_10_02B_ISFJ.cpp
++DNPC_11_03A_INFJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_11_03A_INFJ.cpp
++DNPC_12_03B_INFJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_12_03B_INFJ.cpp
++DNPC_13_04A_INTJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_13_04A_INTJ.cpp
++DNPC_14_04B_INTJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_14_04B_INTJ.cpp
++DNPC_16_05B_ISTP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_16_05B_ISTP.cpp
++DNPC_18_06B_ISFP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_18_06B_ISFP.cpp
++DNPC_19_07A_INFP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_19_07A_INFP.cpp
++DNPC_20_07B_INFP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_20_07B_INFP.cpp
++DNPC_21_08A_INTP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_21_08A_INTP.cpp
++DNPC_22_08B_INTP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_22_08B_INTP.cpp
++DNPC_23_09A_ESTP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_23_09A_ESTP.cpp
++DNPC_24_09B_ESTP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_24_09B_ESTP.cpp
++DNPC_25_10A_ESFP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_25_10A_ESFP.cpp
++DNPC_26_10B_ESFP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_26_10B_ESFP.cpp
++DNPC_27_11A_ENFP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_27_11A_ENFP.cpp
++DNPC_28_11B_ENFP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_28_11B_ENFP.cpp
++DNPC_29_12A_ENTP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_29_12A_ENTP.cpp
++DNPC_30_12B_ENTP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_30_12B_ENTP.cpp
++DNPC_31_13A_ESTJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_31_13A_ESTJ.cpp
++DNPC_33_14A_ESFJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_33_14A_ESFJ.cpp
++DNPC_35_15A_ENFJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_35_15A_ENFJ.cpp
++DNPC_36_15B_ENFJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_36_15B_ENFJ.cpp
++DNPC_37_16A_ENTJ.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_37_16A_ENTJ.cpp
++DNPC_40_Heal_Type.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_40_Heal_Type.cpp
++DNPC_42_AI_02_Type.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_42_AI_02_Type.cpp
++DNPC_51_Raid_Top.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_51_Raid_Top.cpp
++DNPC_52_Raid_Middle.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_52_Raid_Middle.cpp
++DNPC_53_Tolerance.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_53_Tolerance.cpp
++DNPC_70_manor.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_70_manor.cpp
++DNPC_81_Normal.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_81_Normal.cpp
++DNPC_82_Key.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_82_Key.cpp
++DNPC_83_Transform.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_83_Transform.cpp
++DNPC_84_HP.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_84_HP.cpp
++DNPC_85_Aggressive.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_85_Aggressive.cpp
++DNPC_86_AggressiveDummy.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_86_AggressiveDummy.cpp
++DNPC_90_Object.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_90_Object.cpp
++DNPC_96_Patrol06_Monster.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_96_Patrol06_Monster.cpp
++DNPC_277_die_help_Human.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_277_die_help_Human.cpp
++DNPC_278_Heal.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_278_Heal.cpp
++DNPC_279_SP1.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_279_SP1.cpp
++DNPC_Animal.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_Animal.cpp
++DNPC_Animal_Bear.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_Animal_Bear.cpp
++DNPC_Animal_BloodWolf.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_Animal_BloodWolf.cpp
++DNPC_Animal_Fox.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_Animal_Fox.cpp
++DNPC_Animal_Leopard.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_Animal_Leopard.cpp
++DNPC_Animal_Tiger.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_Animal_Tiger.cpp
++DNPC_Animal_Wolf.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_Animal_Wolf.cpp
++DNPC_bookhae_field_boss.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_bookhae_field_boss.cpp
++DNPC_bookhae_field_raid_a.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_bookhae_field_raid_a.cpp
++DNPC_bookhae_field_raid_b.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_bookhae_field_raid_b.cpp
++DNPC_bookhae_guard_monster.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_bookhae_guard_monster.cpp
++DNPC_Patrol01.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\DNPC_Manager\DNPC_Script\DNPC_Patrol01.cpp
++GameEvent.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\GEvent\GameEvent.cpp
++GameEvent.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\GEvent\GameEvent.h
++GameEventParser.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\GEvent\GameEventParser.cpp
++GameEventParser.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\GEvent\GameEventParser.h
++GEventStructure.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\GEvent\GEventStructure.cpp
++GEventStructure.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\GEvent\GEventStructure.h
++PkEvent.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\PKEvent\PkEvent.cpp
++PkEvent.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\PKEvent\PkEvent.h
++PkEventScriptParser.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\PKEvent\PkEventScriptParser.cpp
++PkEventScriptParser.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\Event\PKEvent\PkEventScriptParser.h
++_PkHdr.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktProc\_PkHdr.cpp
++_PktDef.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktProc\_PktDef.h
++Echo.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktProc\Echo.cpp
++Echo.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktProc\Echo.h
++NetEventProc.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktProc\NetEventProc.cpp
++PktBilling.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktProc\PktBilling.cpp
++PktBilling.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\SmallNet\PktProc\PktBilling.h
++Combat.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\Combat.cpp
++Combat.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\Combat.h
++CombatModule.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\CombatModule.cpp
++CombatModule.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\CombatModule.h
++CombatProc.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\CombatProc.cpp
++CombatProc.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\CombatProc.h
++CombatRecord.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\CombatRecord.cpp
++CombatRecord.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\CombatRecord.h
++CombatStruct.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\CombatStruct.h
++ExceptionBattle.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\ExceptionBattle.cpp
++ExceptionBattle.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\ExceptionBattle.h
++NDParty.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\NDParty.cpp
++NDParty.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\NDParty.h
++Organization.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\Organization.cpp
++Organization.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\Organization.h
++Location_Tracking_System.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\Location_Tracking_System.cpp
++Location_Tracking_System.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\Location_Tracking_System.h
++LocationStruct.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\LocationStruct.h
++TaxSystem.cpp
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\TaxSystem.cpp
++TaxSystem.h
i:{a5ef0e96-fe77-40db-8679-************}:D:\Project\NDServer\User\TaxSystem.h
