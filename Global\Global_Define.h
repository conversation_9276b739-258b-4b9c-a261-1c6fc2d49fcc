#ifndef _THIS_IS_ND_GAME_SERVER_HEADER_GLOBAL_CONSTATNTS__3243SDLFHL34__
#define _THIS_IS_ND_GAME_SERVER_HEADER_GLOBAL_CONSTATNTS__3243SDLFHL34__

#include "StdAfx.h"

/* ============================================================================
	�� ��ȣ ����
============================================================================ */
#define _MAP_NAME_SOONGSAN_				(1)		// ����/�Ҹ��纻��
#define _MAP_NAME_CHUNJIN_				(2)		// õ��/���溻��
#define _MAP_NAME_TAESAN_				(3)		// �»�/�츲�ͺ���
#define _MAP_NAME_JAHABIDONG_			(4)		// ���Ϻ�/��ú���
#define _MAP_NAME_MOODANGSAN_			(5)		// �����/���纻��
#define _MAP_NAME_YOUMMASAN_			(6)		// ������/��������
#define _MAP_NAME_HANAM_				(7)		// �ϳ�/����
#define _MAP_NAME_HABOOCK_				(8)		// �Ϻ�/������
#define _MAP_NAME_SANDONG_				(9)		// �굿/����
#define _MAP_NAME_SANSU_				(10)	// �꼭/�뵿
#define _MAP_NAME_GANGSU_NAMCHANG_		(11)	// ����/��â
#define _MAP_NAME_SEOJANG_				(12)	// ����/��â�߼���

#define _MAP_NAME_JULGANG_HANGJOO_		(13)	// ����/����
#define _MAP_NAME_JULGANG_HANGJOO2_		(14)	// ����/���ֱ����� : �̺�Ʈ�� ���� ��а� ���������� ����Ѵ�.2005/08/11
#define _MAP_NAME_NAMMAN_				(15)	// ����/����
#define _MAP_NAME_YODONG_				(16)	// �䵿/���
#define _MAP_NAME_HAPBI_				(17)	// �ȼ�/�պ�
#define _MAP_NAME_YONGMOONDONG_			(18)	// �빮��
#define _MAP_NAME_GEOMGANGDONG_			(19)	// �ݰ���
#define _MAP_NAME_HYULSAPYUNG_			(20)	// ������.
#define _MAP_NAME_PAEWANGDONG_			(21)	// �пյ�.
#define _MAP_NAME_MIJUNG_				(22)	// �пյ�.
#define _MAP_NAME_BOOKHAE_				(23)	// ����.

const short g_sMapProperty[_MAP_MAX_NUM] = 
{ 
	0, 	// 
	1,	//1		// ����/�Ҹ��纻��
	1,	//2		// õ��/���溻��
	-1,	//3		// �»�/�츲�ͺ���
	-1,	//4		// ���Ϻ�/��ú���
	1,	//5		// �����/���纻��
	-1,	//6		// ������/��������
	1,	//7		// �ϳ�/����
	-1,	//8		// �Ϻ�/������
	1,	//9		// �굿/����
	-1,	//10		// �꼭/�뵿
	0,	//11		// ����/��â
	0,	//12		// ����	
	0,	//13		// ����/����
	0,	//14		// ����/���ֱ����� : �̺�Ʈ�� ���� ��а� ���������� ����Ѵ�.2005/08/11
	0,	//15		// ����/����
	0,	//16		// ���/���
	0,	//17		// �ȼ�/�պ�
	1,	//18		// �빮��
	-1,	//19		// �ݰ���
	0,	//20		// ������
	0,	//21		// �пյ�
	0,	//22		// ����
	0,	//23		// ����
} ;

/* =====================================================================================
	���� ����
===================================================================================== */
#define _FILE_ND_SERVER_ENVIRONMENT_	"nfofile/ServerEnv.inf"				// txt ����.
#define _FILE_ND_ITEM_TABLE_			"nfofile/item_table.bit"			// ������ ����. bit
#define _FILE_ND_SET_SCRIPT_			"nfofile/Set_Script.txt"			// ��Ʈ ������ ��ũ��Ʈ.
#define _FILE_ND_MONSTER_REF_			"nfofile/monster_ref.bmo"			// ���� ��ü ���� : bmo
#define _FILE_ND_COLLECTIVE_DROP_INFO_	"nfofile/collectivedropinfo.inf"	// ���͵��� �������� ����ϴ� DropInfo
#define _FILE_ND_QUESTSCRIPT			"nfofile/quest/questscript.xqs"		// ����Ʈ ����
#define _FILE_ND_NPCBASICSCRIPT_		"nfofile/npc/npcbasicscript.txt"	// Npc ���� ����
#define _FILE_ND_LEVEL_TABLE_			"nfofile/level_table.inf"			// ���� ���̺�
#define _FILE_ND_SKILL_NAME_			"nfofile/skill_table.bsk"			// ��ų ���̺�
#define _FILE_ND_DNPC_SKILL_NAME_		"nfofile/dnpc_skill_table.bsk"		// DNPC ��ų ���̺�
#define _FILE_ND_CLASS_SKILL_REF_		"nfofile/class_skill.txt"

#define _FILE_ND_EFFECT_SCRIPT_			"nfofile/effect_script.txt"
#define _FILE_ND_PVP_APPLY_				"nfofile/effect_pvp_apply.txt"

#define _FILE_ND_MASTERY_TABLE_			"nfofile/mastery_table.bms"			// �����͸� ���̺� 
#define _FILE_ND_BATTLE_PARAMETER_		"nfofile/ND_Battle_Parameter.txt"	// ������ ���̴� �Ķ����
#define _FILE_ND_NPC_BUFF_TABLE_		"nfofile/NPC_Buff_Table.txt"		// NPC ���� ���̺� 

#define _FILE_ND_MIX_TABLE_             "nfofile/item_Mix_table.txt"        // ������ �ͽ� ���� �߰� ���� �Ӽ� ������ ������ ȭ��.

#define _FILE_ND_EVENT_MAIN_SCRIPT_		"nfofile/event_main.txt"		// �ܺ� �̺�Ʈ ���� ��ũ��Ʈ(�̺�Ʈ ��ü ����)
#define _FILE_ND_EVENT_PACKAGE_SCRIPT_	"nfofile/event_package.txt"			// �ܺ� �̺�Ʈ ���� ��ũ��Ʈ(�̺�Ʈ ���� ����)

#define _FILE_ND_GAME_EVENT_SCRIPT_		"nfofile/GameEventScript.txt"		// ���� �� �̺�Ʈ ��ũ��Ʈ.

#define _FILE_ND_COLLECTIONITEM_SCRIPT_	"nfofile/Collection.txt"			// ��ǰ���� ��ũ��Ʈ
#define _FILE_ND_RESOURCEITEM_SCRIPT_	"nfofile/Resource.txt"				// ������ ��ũ��Ʈ

#define _FILE_ND_INCHANT_TABLE_			"nfofile/InchantTable.txt"			// ���� ��ũ��Ʈ.

#define _FILE_ND_DEFAULT_QUESTRANK_		"nfofile/DefaultQuestRank.txt"		// ����Ʈ ����Ʈ ��ũ ����

#define _FILE_ND_ENDING_MSG_			"nfofile/ending_msg.txt"

#define _HONOR_REF_TABLE_FILE_NAME		"nfofile/fame_ref.txt"

#define _FILE_ND_MOVE_VILLAGE_			"nfofile/Move_Village.xms"			// ǥ�� �̵� ��ũ��Ʈ.

#define _FILE_ND_DROP_NCODE_			"nfofile/Item_Drop_NCode.txt"		// ���� ������ ������ �и�. Monster Ref v1.4����.

#define _FILE_ND_FATIGUE_PENALTY_LIST_	"nfofile/Fatigue_Penalty_List.txt"	// ��Ʈ�� shutdown �Ƿε�	

#define _FILE_ND_ESCAPE_SCRIPT_			"nfofile/escape.txt"

#define _FILE_ND_PENALTY_OF_ZONE_		"nfofile/penalty_of_zone.txt"		// ������ ����ȥ�� �г�Ƽ ���� ����.

#ifdef _PD_ITEM_RENEWAL_1008_
#define _FILE_ND_WEAPON_COMBINE_COST_	"nfofile/weapon_combine_cost.txt"	// ���� ���� ���; 
#endif

#define _FILE_ND_FREE_MATCH_SCRIPT_		"nfofile/FreeMatch.txt"

#define _FILE_ND_RECALL_MONSTER_		"nfofile/Monster_Summon.txt"		// �ܵ����� ��ȯ�� ���̴� ���� ����.

/* =====================================================================================
	���� ���� ���� : nfo
===================================================================================== */
#define _FILE_ZONE_SOONGSAN_			"nfofile/zone/map_sorim.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_CHUNJIN_				"nfofile/zone/map_gaebang.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_TAESAN_				"nfofile/zone/map_noklim.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_JAHABIDONG_			"nfofile/zone/map_beegoong.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_MOODANGSAN_			"nfofile/zone/map_moodang.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_YOUMMASAN_			"nfofile/zone/map_magyo.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_HANAM_				"nfofile/zone/map_jungju.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_HABOOCK_				"nfofile/zone/map_seokgaja.nfo"	//ZONEDATAFILE
#define _FILE_ZONE_SANDONG_				"nfofile/zone/map_jaenam.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_SANSU_				"nfofile/zone/map_daedong.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_GANGSU_NAMCHANG_		"nfofile/zone/map_namchang.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_SEOJANG_				"nfofile/zone/map_seojang.nfo"//ZONEDATAFILE
#define _FILE_ZONE_JULGANG_HANGJOO_		"nfofile/zone/map_hangjoo.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_HANGJOO2_			"nfofile/zone/map_hangjoo2.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_NAMMAN_				"nfofile/zone/map_gyodo.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_YODONG_				"nfofile/zone/map_yodong.nfo"		//ZONEDATAFILE
#define _FILE_ZONE_HAPBI_				"nfofile/zone/map_hapbi.nfo"
#define _FILE_ZONE_YONGMOONDONG_		"nfofile/zone/map_yongmoondong.nfo"	// 
#define _FILE_ZONE_GEOMGANGDONG_		"nfofile/zone/map_geomgangdong.nfo"	// 
#define _FILE_ZONE_HYULSAPYUNG_			"nfofile/zone/map_hyulsapyung.nfo"	//ZONEDATAFILE
#define _FILE_ZONE_PAEWANGDONG_			"nfofile/zone/map_Paewangdong.nfo"	//
#define _FILE_ZONE_MIJUNG_				"nfofile/zone/map_Mijung.nfo"	//
#define _FILE_ZONE_BOOKHAE_				"nfofile/zone/map_Bookhae.nfo"	//


/* =====================================================================================
	Patrol DNPC ���� ����	: inf
===================================================================================== */
#define _FILE_PATROLSPAWN_SOONGSAN_			"nfofile/mob_spawn/map_sorim_patrol.inf"
#define _FILE_PATROLSPAWN_CHUNJIN_			"nfofile/mob_spawn/map_gaebang_patrol.inf"
#define _FILE_PATROLSPAWN_TAESAN_			"nfofile/mob_spawn/map_noklim_patrol.inf"
#define _FILE_PATROLSPAWN_JAHABIDONG_		"nfofile/mob_spawn/map_beegoong_patrol.inf"
#define _FILE_PATROLSPAWN_MOODANGSAN_		"nfofile/mob_spawn/map_moodang_patrol.inf"
#define _FILE_PATROLSPAWN_YOUMMASAN_		"nfofile/mob_spawn/map_magyo_patrol.inf"
#define _FILE_PATROLSPAWN_HANAM_			"nfofile/mob_spawn/map_jungju_patrol.inf"
#define _FILE_PATROLSPAWN_HABOOCK_			"nfofile/mob_spawn/map_seokgaja_patrol.inf"
#define _FILE_PATROLSPAWN_SANDONG_			"nfofile/mob_spawn/map_jaenam_patrol.inf"
#define _FILE_PATROLSPAWN_SANSU_			"nfofile/mob_spawn/map_daedong_patrol.inf"
#define _FILE_PATROLSPAWN_GANGSU_NAMCHANG_	"nfofile/mob_spawn/map_namchang_patrol.inf"
#define _FILE_PATROLSPAWN_SEOJANG_			"nfofile/mob_spawn/map_seojang_patrol.inf"
#define _FILE_PATROLSPAWN_JULGANG_HANGJOO_	"nfofile/mob_spawn/map_hangjoo_patrol.inf"
#define _FILE_PATROLSPAWN_HANGJOO2_			"nfofile/mob_spawn/map_hangjoo_patrol.inf"
#define _FILE_PATROLSPAWN_NAMMAN_			"nfofile/mob_spawn/map_gyodo_patrol.inf"
#define _FILE_PATROLSPAWN_YODONG_			"nfofile/mob_spawn/map_yodong_patrol.inf"
#define _FILE_PATROLSPAWN_HAPBI_			"nfofile/mob_spawn/map_hapbi_patrol.inf"
#define _FILE_PATROLSPAWN_YONGMOONDONG_		"nfofile/mob_spawn/map_yongmoondong_patrol.inf"
#define _FILE_PATROLSPAWN_GEOMGANGDONG_		"nfofile/mob_spawn/map_geomgangdong_patrol.inf"
#define _FILE_PATROLSPAWN_HYULSAPYUNG_		"nfofile/mob_spawn/map_hyulsapyung_patrol.inf"
#define _FILE_PATROLSPAWN_PAEWANGDONG_		"nfofile/mob_spawn/map_paewangdong_patrol.inf"
#define _FILE_PATROLSPAWN_MIJUNG_			"nfofile/mob_spawn/map_mijung_patrol.inf"
#define _FILE_PATROLSPAWN_BOOKHAE_			"nfofile/mob_spawn/map_bookhae_patrol.inf"

/* =====================================================================================
	Tile ����. ( ���� ����.xpft )
===================================================================================== */
#define _FILE_TILE_SOONGSAN_			"nfofile/obbdata/map_sorim_tile.xpft"			//TILEDATAFILE
#define _FILE_TILE_CHUNJIN_				"nfofile/obbdata/map_gaebang_tile.xpft"
#define _FILE_TILE_TAESAN_				"nfofile/obbdata/map_noklim_tile.xpft"
#define _FILE_TILE_JAHABIDONG_			"nfofile/obbdata/map_beegoong_tile.xpft"
#define _FILE_TILE_MOODANGSAN_			"nfofile/obbdata/map_moodang_tile.xpft"
#define _FILE_TILE_YOUMMASAN_			"nfofile/obbdata/map_magyo_tile.xpft"
#define _FILE_TILE_HANAM_				"nfofile/obbdata/map_Jungju_tile.xpft"
#define _FILE_TILE_HABOOCK_				"nfofile/obbdata/map_seokgaja_tile.xpft"
#define _FILE_TILE_SANDONG_				"nfofile/obbdata/map_jaenam_tile.xpft"
#define _FILE_TILE_SANSU_				"nfofile/obbdata/map_daedong_tile.xpft"
#define _FILE_TILE_GANGSU_NAMCHANG_		"nfofile/obbdata/map_namchang_tile.xpft"
#define _FILE_TILE_SEOJANG_				"nfofile/obbdata/map_seojang_tile.xpft"
#define _FILE_TILE_JULGANG_HANGJOO_		"nfofile/obbdata/map_hangjoo_tile.xpft"
#define _FILE_TILE_HANGJOO2_			"nfofile/obbdata/map_hangjoo2_tile.xpft"
#define _FILE_TILE_NAMMAN_				"nfofile/obbdata/map_gyodo_tile.xpft"
#define _FILE_TILE_YODONG_				"nfofile/obbdata/map_yodong_tile.xpft"
#define _FILE_TILE_HAPBI_				"nfofile/obbdata/map_hapbi_tile.xpft"
#define _FILE_TILE_YONGMOONDONG_		"nfofile/obbdata/map_yongmoondong_tile.xpft"
#define _FILE_TILE_GEOMGANGDONG_		"nfofile/obbdata/map_geomgangdong_tile.xpft"
#define _FILE_TILE_HYULSAPYUNG_			"nfofile/obbdata/map_hyulsapyung_tile.xpft"
#define _FILE_TILE_PAEWANGDONG_			"nfofile/obbdata/map_paewangdong_tile.xpft"
#define _FILE_TILE_MIJUNG_				"nfofile/obbdata/map_mijung_tile.xpft"
#define _FILE_TILE_BOOKHAE_				"nfofile/obbdata/map_bookhae_tile.xpft"


/* =====================================================================================
	Obb ����. ( ���� ����.xobb )
===================================================================================== */
#define _FILE_OBB_SOONGSAN_				"nfofile/obbdata/map_sorim_obb.xobb"			//OBBDATAFILE
#define _FILE_OBB_CHUNJIN_				"nfofile/obbdata/map_gaebang_obb.xobb"
#define _FILE_OBB_TAESAN_				"nfofile/obbdata/map_noklim_obb.xobb"
#define _FILE_OBB_JAHABIDONG_			"nfofile/obbdata/map_beegoong_obb.xobb"
#define _FILE_OBB_MOODANGSAN_			"nfofile/obbdata/map_moodang_obb.xobb"
#define _FILE_OBB_YOUMMASAN_			"nfofile/obbdata/map_magyo_obb.xobb"
#define _FILE_OBB_HANAM_				"nfofile/obbdata/map_Jungju_obb.xobb"
#define _FILE_OBB_HABOOCK_				"nfofile/obbdata/map_seokgaja_obb.xobb"
#define _FILE_OBB_SANDONG_				"nfofile/obbdata/map_jaenam_obb.xobb"
#define _FILE_OBB_SANSU_				"nfofile/obbdata/map_daedong_obb.xobb"
#define _FILE_OBB_GANGSU_NAMCHANG_		"nfofile/obbdata/map_namchang_obb.xobb"
#define _FILE_OBB_SEOJANG_				"nfofile/obbdata/map_seojang_obb.xobb"
#define _FILE_OBB_JULGANG_HANGJOO_		"nfofile/obbdata/map_hangjoo_obb.xobb"
#define _FILE_OBB_HANGJOO2_				"nfofile/obbdata/map_hangjoo2_obb.xobb"
#define _FILE_OBB_NAMMAN_				"nfofile/obbdata/map_gyodo_obb.xobb"
#define _FILE_OBB_YODONG_				"nfofile/obbdata/map_yodong_obb.xobb"
#define _FILE_OBB_HAPBI_				"nfofile/obbdata/map_hapbi_obb.xobb"
#define _FILE_OBB_YONGMOONDONG_			"nfofile/obbdata/map_yongmoondong_obb.xobb"
#define _FILE_OBB_GEOMGANGDONG_			"nfofile/obbdata/map_geomgangdong_obb.xobb"
#define _FILE_OBB_HYULSAPYUNG_			"nfofile/obbdata/map_hyulsapyung_obb.xobb"
#define _FILE_OBB_PAEWANGDONG_			"nfofile/obbdata/map_paewangdong_obb.xobb"
#define _FILE_OBB_MIJUNG_				"nfofile/obbdata/map_mijung_obb.xobb"
#define _FILE_OBB_BOOKHAE_				"nfofile/obbdata/map_bookhae_obb.xobb"

/* =====================================================================================
	������ NPC ��ũ��Ʈ ȭ��	
===================================================================================== */
#define _FILE_NPCSCRIPT_SOONGSAN_			"nfofile/npc/sorim_npcscript.txt"
#define _FILE_NPCSCRIPT_CHUNJIN_			"nfofile/npc/gaebang_npcscript.txt"
#define _FILE_NPCSCRIPT_TAESAN_				"nfofile/npc/noklim_npcscript.txt"
#define _FILE_NPCSCRIPT_JAHABIDONG_			"nfofile/npc/beegoong_npcscript.txt"
#define _FILE_NPCSCRIPT_MOODANGSAN_			"nfofile/npc/moodang_npcscript.txt"
#define _FILE_NPCSCRIPT_YOUMMASAN_			"nfofile/npc/magyo_npcscript.txt"
#define _FILE_NPCSCRIPT_HANAM_				"nfofile/npc/Jungju_npcscript.txt"
#define _FILE_NPCSCRIPT_HABOOCK_			"nfofile/npc/seokgaja_npcscript.txt"
#define _FILE_NPCSCRIPT_SANDONG_			"nfofile/npc/jaenam_npcscript.txt"
#define _FILE_NPCSCRIPT_SANSU_				"nfofile/npc/daedong_npcscript.txt"
#define _FILE_NPCSCRIPT_GANGSU_NAMCHANG_	"nfofile/npc/namchang_npcscript.txt"
#define _FILE_NPCSCRIPT_SEOJANG_			"nfofile/npc/seojang_npcscript.txt"
#define _FILE_NPCSCRIPT_JULGANG_HANGJOO_	"nfofile/npc/hangjoo_npcscript.txt"
#define _FILE_NPCSCRIPT_HANGJOO2_			"nfofile/npc/hangjoo2_npcscript.txt"
#define _FILE_NPCSCRIPT_NAMMAN_				"nfofile/npc/gyodo_npcscript.txt"
#define _FILE_NPCSCRIPT_YODONG_				"nfofile/npc/yodong_npcscript.txt"
#define _FILE_NPCSCRIPT_HAPBI_				"nfofile/npc/hapbi_npcscript.txt"
#define _FILE_NPCSCRIPT_YONGMOONDONG_		"nfofile/npc/yongmoondong_npcscript.txt"
#define _FILE_NPCSCRIPT_GEOMGANGDONG_		"nfofile/npc/geomgangdong_npcscript.txt"
#define _FILE_NPCSCRIPT_HYULSAPYUNG_		"nfofile/npc/hyulsapyung_npcscript.txt"
#define _FILE_NPCSCRIPT_PAEWANGDONG_		"nfofile/npc/paewangdong_npcscript.txt"
#define _FILE_NPCSCRIPT_MIJUNG_				"nfofile/npc/mijung_npcscript.txt"
#define _FILE_NPCSCRIPT_BOOKHAE_			"nfofile/npc/bookhae_npcscript.txt"

#define PK_EVENT_SCRIPT_FILE_PATH	_T("nfofile/pk_event.script")


/* ======================
Reference File Name
========================*/

#define _FILE_EX_BATTLE_ROOM_INFO_REF		"nfofile/ExBattle/RoomInfoScript.txt"	// Exception Battle room info file name
#define _FILE_DNPC_PATH						"nfofile/map_dnpc_path.inf"				//DNPC path data
#define _NIC_REF_TABLE_FILE_NAME			"nfofile/NicNameRef.txt"				// Nicname referece table file name 
#define _FILE_ND_MANOR_INFORMATION_			"nfofile/manor/manor_map_script.txt"	// Manor Information.


// Path string
#define _DIR_OBB_							"nfofile/obbdata/"
#define _DIR_TILE_							"nfofile/obbdata/"
#define _DIR_EVENT_							"nfofile/ExBattle/"
#define _DIR_REGION_						"nfofile/zone/"
#define _DIR_MANOR_							"nfofile/manor/"


//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////


enum USER_BILLGRADE
{
	BILLGRADE_NORMAL=0,		// �Ϲ� ���� �����.
	BILLGRADE_FIXED,			// ������ ��� �����.
	BILLGRADE_PREMIUM_1,		// �����̾� 1 �����.
	BILLGRADE_PREMIUM_2,		// �����̾� 2 �����.
	BILLGRADE_PREMIUM_3,		// �����̾� 3 �����.
}  ;

enum CONPOS_GRADE
{
	POSGRADE_HOME=0,		// ������ ����.
	POSGRADE_PC_NORMAL,		// ȸ�� �ǽù� ����.
	POSGRADE_PC_PREMIUM,	// �����̾� �ǽù� ����.
} ;

enum SKILLEXPBONUS_MODE
{
	SB_MODE_NONE=0,				// ���ʽ� ����.
	SB_MODE_ADD_50_PERCENT,		// 50% �߰�.
	SB_MODE_ADD_100_PERCENT,	// 100% �߰�.
	SB_MODE_ADD_150_PERCENT,	// 150% �߰�.
	SB_MODE_ADD_200_PERCENT		// 200% �߰�.
} ;

//////////////////////////////////////////////////////////////////////////
#define _INT_LIMIT				(0x7fffffff)
#define _UINT_LIMIT				(0xffffffff)


// �ʱ����ӽ� ���� ����.
#define DEFAULT_INITIAL_NOENEMY_TIME	(15U)
#define DEFAULT_FM_NOENEMY_TIME			(3U)

/* =====================================================================================
	������/���� ����,���� �α� ����
===================================================================================== */
#define _FILE_LOG_ITEM_					"item/Item_Log.txt"
#define _FILE_LOG_MONSTER_				"LogFiles/Monster/Monster_Log.txt"


/* =====================================================================================
	Ʈ���̵� ���� ����.
===================================================================================== */
#define TRADE_STATUS_NORMAL             0 // �ƹ��͵� �ƴ� ����.
#define TRADE_STATUS_REQUEST            1 // Ʈ���̵� ��û�� ����.
#define TRADE_STATUS_WAIT               2 // Ʈ���̵� ��û�� �ް� �㰡 ���� ���� â�� �߰ԵǴ� ����.
#define TRADE_STATUS_TRADEING           3 // Ʈ���̵� ��. 
#define TRADE_STATUS_SHOPOPEN           4 // ���� ���� ���»���.
#define TRADE_STATUS_PSTCLOSE           5 // ���� ���� �ӽ� �ݱ�.

#define TRADE_STATUS_USEELIXIR          6 // ���� �Դ� ����.

#define TRADE_NO_EMPTY_SLOT             7777 // Ʈ���̵� ������ ������ ���Ѵ�. 
#define TRADE_BORROW_SLOT_NOTYET        7777 // ������ ���� ������ �ʾҴ�. 
#define TRADE_NO_EMPTY_ITEM_SLOT        8888 // ������ ���� �� ���� ����. 

#define PERSNAL_SHOP_SLOT_EMPTY         8888		// ���� ������ ������ ���Ѵ�.
#define PSSHOP_BORROW_SLOT_NOTYET       9999		// ���� ���� �ʾҴ�. 
#define PSSHOP_MAX_ITEM_LIST            9           // �ǸŰ��� �ִ� ������.
#define PSSHOP_MAX_SHOP_NAME            37          // �ִ� ���� �̸� ������.
#define PSSHOP_MAX_WATCHER_NUM          10          // �ִ� ����� ����.

// �α��ν� ������ ��ٸ��� �ð�
#define _LOGIN_TIME_GAME_WAIT_				10		// �α��ο� ������ �� �� �ð����� �� ������ �������� ���ϸ� ������ ó���Ѵ�.
#define _LOGIN_TIME_LOGIN_WAIT_				20		// �α��� ������ 20 �ʵ����� �� ������ ���� ���ο� ������� �ٽ� �α������� ���Ѵ�. 	

// ������ �ƽ� �ο� : 
#define _MAX_USER_SLOT_						1800	// �Ƹ� �������� '�� ��'�� ����� ���� ���� ���̴�. �׷��� ������ �Ŵϱ� �� �� 

#define _SERVER_UPPER_MAX_					1700	// �ο��� upper�� �Ѿ�� �� �̻� accept ���� �ʰ�, 
#define _SERVER_LOWER_MAX_					1700	// �ο��� lower ������ �������� ��ٸ���. 

// �ʱ�ȭ ���� ��� 
#define _SERVER_INIT_ACCEPT_THREAD_			0x0001	// Accept�����尡 �����ϸ� g_bInitialize ������ �� ���� |(or) �����Ѵ�.
#define _SERVER_INIT_RECV_THREAD_			0x0002	// Recv Thread 
#define _SERVER_INIT_WORKER_THREAD_			0x0004	// Worket Thread : IOCP


#define _SERVER_INIT_OK_					0x0007	// ��� �ʱ�ȭ�� �����ߴ��� �Ǵ��Ϸ���, 
//													// g_bInitialize == �䰪 �Ѵ�.

// Thread ���� 
#define _THREADNUMBER_PACKET_RECV_			4		//4	// recv ó���ϴ� �Լ�.

// ������ ��Ŷ ����	
#define _SERVER_AUTHOR_						1003
#define _PING_PORT_							21000


#define _MAX_PACKET_SEND_SIZE				4092

/* ============================================================================
	���� ���ɾ� 
============================================================================ */
#define _CMD_NORMAL_PROCESSING_				0				// ���� ó���Ǿ���
#define _CMD_SERVER_SHUTDOWN_				1				// ���� ���� �޼���
#define _CMD_ABNORMAL_COMMAND_				2				// ���ɾ ����. ǥ����.



/* ============================================================================
	ERROE or EXCEPTION Constant
============================================================================ */
#define _ERRNO_CONNECT_RECV_				-1				// recv ����.
#define _ERRNO_CONNECT_USERENTER_FAIL_		-2				// zone->user_enter ����
#define _ERRNO_CONNECT_INCORRECTPACKET_		-3				// �ٸ� ��Ŷ�� �޾Ҵ�.


/* =====================================================================================
	User Timeout �� : ��Ŷ�� �� �ð����� �ȹ�����, ƨ���.
===================================================================================== */
#ifdef _PD_KILL_GHOST_

	#define USER_TIMEOUT_IN_FIELD				1800000			// �ʵ忡��.
	#define USER_TIMEOUT_READY					360000			// 
	#define USER_TIMEOUT_LOBBY					600000			// �κ񿡼��� Ÿ�Ӿƿ�.


	#define USER_TIMEOUT_WHILE_LOADING			900000
	#define USER_TIMEOUT_NORMAL					240000			// �Ϲ����� ��Ȳ : 4��

#else 

	#define USER_TIMEOUT_IN_FIELD				60000			// �ʵ忡��.
	#define USER_TIMEOUT_READY					240000			// 
	#define USER_TIMEOUT_LOBBY					240000			// �κ񿡼��� Ÿ�Ӿƿ�.


	#define USER_TIMEOUT_WHILE_LOADING			900000			// �� �ε� 15�а� ��ٸ���.
	#define USER_TIMEOUT_NORMAL					240000			// �Ϲ����� ��Ȳ : 4��

#endif



/* ============================================================================
	��Ʈ��ũ ��� : �б� ����, ���� ���� ũ��� ���⿡ �� ��Ŷ�� ��
============================================================================ */
#define _RECV_BUF_SIZE_					2048
//#define _SEND_BUF_SIZE_					1280
#define _BIG_UNIT_SIZE_					1536			// 
#define _MAX_PACKET_SLOT_				196608     
#define _RECV_DSBUF_SIZE_				32768


// �������� ����
//#define _COUNT_MAX_GROUP_				20

#define _COUNT_MAX_GROUP_				122

// ������ ��ȣ�� ������ 2���� �ε����� ���.
#define _GR_INDEX_TRAIN_				120
#define _GR_INDEX_UNION_				121


/* ============================================================================
	���� ���� ���� : m_sUserState
============================================================================ */
#define US_NONSTATE						0
#define US_READY_ENTER					1		// ���� ������ ���� : ip, socket �̿��� ���� ����
#define US_AFTER_ENTER					2		// EnterGame ��Ŷ�� �ް�, DS �� ���� ��û�� �� ���� ����.
#define US_DISCONNECT					3

#define US_WAIT_PLIST					6		// ������ �÷��̾� ����Ʈ ��û�� ��ٸ��� ���̴�
#define US_CHARAC_SELECT				10		// �α��ο� �����Ͽ� ĳ���� ���� â�� ����
#define US_CHARAC_CREATE				11		// ĳ���� ������û��Ŷ�� �ް�, ds���� ������⸦ ��ٸ�.
#define US_CHARAC_DELETE				12		// ĳ���� ���� ��Ŷ�� �ް�, ds���� ����� ���⸦ ��ٸ�.
#define US_READY_FIELD					15		// �������� �ʱ� ��ġ�� ���� ����. : signal �� ������ ���� ���·� ������Ʈ �Ѵ�. 
// �� ���±����� ������ ������ ���, �ƹ��͵� ���ص� ��.( ��, DB�� �α��� ���´� ������ �� )

#define US_FIELD_ALIVE					30		// ĳ���͸� �����Ͽ� ���� �ʵ忡 �� ���� - �������
#define US_FIELD_WILL					31		// ��� ����
#define US_FIELD_COMA					32		// ȥ�� ����
#define US_SERVER_MOVE					35		// ���� �̵���. �̶��� �������ų�, logout ��� ����. ��, Ÿ�̸ӿ� ���� �޼�����..

#define _CHARAC_BINSA_VITAL_			-1		// ��� �����϶��� ü��
#define _CHARAC_HONSU_VITAL_			-2		// ȥ�� �����϶��� ü��

/* ============================================================================
	������ ���� ����
============================================================================ */
#define USS_NORMAL						0		// ������ ��
#define USS_DISCONNECT					1		// ������ ��������. 


/* ============================================================================
	�������� ������ error message
============================================================================ */
enum EN_USER_ERROR_MSG
{
	_US_ERR_MSG_OTHER_LOGIN_ = 0,
} ;


/* =====================================================================================
	���� ����
===================================================================================== */
#define _MOONPA_NO_NANGIN_				0		// ����
#define _MOONPA_NO_GAEBANG_				1		// ����
#define _MOONPA_NO_BEEGOONG_			2		// ���(���� �豳)
#define _MOONPA_NO_SORIM_				3		// �Ҹ�
#define _MOONPA_NO_NOKLIM_				4		// �츲
#define _MOONPA_NO_MOODANG_				5		// ����
#define _MOONPA_NO_MAGYO_				6		// ����
#define _MOONPA_NO_SEGA_				7		// ����
#define _MOONPA_NO_HEOKRYONG_			8		// ����

#define _NUM_MOONPA_					9		// �ִ� ���ļ�(����Ʈ�� ������ ���)		 


#define _NUM_CLASS_						8		// ��å�� ���� : 0�� �������� �ʴ´�. 

// 0 : ����, 1 : �鵵, -1 : �浵
const short g_sMoonpaProperty[_NUM_MOONPA_] = 
{ 
	0, 	// ����0
	1,	// ����1
	-1,	// ���2
	1,	// �Ҹ�3
	-1,	// �츲4
	1,	// ����5
	-1,	// ����6
	0,	// ����7
	0	// ����8
} ;


enum EN_CHARAC_RELATIONSHIP
{
	en_charac_relation_none=0,
	en_charac_relation_friend,
	en_charac_relation_enemy,
	en_charac_relation_count
} ;

enum EN_CHARAC_PK_HONOR_VALUE
{
	en_charac_pk_killer_honor=0,
	en_charac_pk_killer_fame,
	en_charac_pk_dead_honor,
	en_charac_pk_dead_fame,
	en_charac_pk_honor_count
} ;

#define _ALL_MOONPA_APPLY_				20		// ��ų�� ���, ��� ���Ŀ� ����.

// Ư�� ����
#define _SPECIAL_NO_NONE_				0		// ���� 
#define _SPECIAL_NO_JAGAEKRU_			1		// �ڰ���
#define _SPECIAL_NO_DONGCHANG_			2		// ��â


/* ============================================================================
	���ӳ��� �ִ밪�� �Ǵ� �⺻����.
============================================================================ */
#define _DIST_CULLING_					900			// �ø� �⺻�� : �̵��̳� �Ϲ�ä���� �Ÿ� �ø� ��.

#define _TIME_AFTER_ATTACK_LEFT_		7000U		// ���� �ްų� ���� ���� ���� �α׾ƿ� ���Ѵ�. �� �ȿ��� �α׾ƿ� ��Ŷ�� �����Ѵ�. 

#define _COUNT_MONSTER_TYPE_			10000		// ������ �ִ� '��'��
#define _MAX_MONSTER_SLOT_				5000		// ������ �ִ� ��

#define _MAX_MOB_DROP_COUNT_			12			// ���Ͱ� ����ϴ� �������� �ִ밹��
#define _MAX_MOB_SLOT_COUNT_			30			// ���۷��� ���� ���� ��� ���� ����.

#define _SPAWN_CHECK_TIME_				10000		// ���� üũ ���� : 16�ʸ��� ������ �ǽ��Ѵ�. 

#define _MAX_MONEY_						0xffffffffU	// �� �ִ밪.
#define _MAX_ITEM_COUNT_				255			// ������ ���� �ִ밪.
#define _RANDOM_ITEM_TYPE_				(100)		// ���Ͱ� �� ���� ������ Ÿ������ �� ���� Ÿ��.
#define _UNI_WEAPON_ITEM_TYPE_			(101)
#define _UNI_CLOTH_ITEM_TYPE_			(102)
#define _ITEM_RANK_KEY_MAKER_			(1000000)

#define _PARTY_VALID_TIME_				12000		// ��Ƽ ��û ��ȿ�ð�.
#define _PARTY_MAX_MEMBER_				9			// ��Ƽ�� �ִ� �ο���

// ĳ���� �ڵ� ��� �ð� �� ������ ȸ�� ���� ���� �ð�
#define _TIME_DELAY_AFTER_RECOVER_ITEM_	15000		// Ư�� ȸ�� ������ ����� ���� �ð� 15��.


#define _NDD_MAX_ORG_IN_ALLY_			(5)			// ���� ������ �ִ� ������ ���� 5�̴�.
#define _TIME_ALLY_REQUEST_VALID_		(15000U)	// ���� ������ ���� ��û�� ��ȿ �ð�.

//#define _COUNT_CHARAC_CLASS_			12			// ĳ������ Ŭ���� ����

// ����Ǯ�� �� ��� ���.
#define _COST_SAVE_POSITION_			10U

/*------------------------------------------------------------------------
	 �� ����ġ �α� ����� �� �׼��� �� HHH
/------------------------------------------------------------------------*/ 
#define _MAX_DIFF_MONEY_                100000  /* 10���̻� ���̳��� �α׸� �����. */ 

enum EN_CHARAC_CLASS
{
	en_chr_class_none=0,		// 0 ���� 
	en_chr_class_battle,		// 1 ������
	en_chr_class_healer,		// 2 ġ����
	en_chr_class_complete,		// 3 ������
	en_chr_class_speller,		// 4 ���
	en_chr_class_murder,		// 5 ���
	en_chr_class_hosim,			// 6 ȣ��
	en_chr_class_jusool,		// 7 �ּ�
	en_chr_class_woigong,		// 8 �ܰ�
	en_chr_class_sohwan,		// 9 ��ȯ
	en_chr_class_sundo,			// 10 ����
	en_chr_class_mado,			// 11 �ܵ�
	en_chr_class_count			// 12 ����.
} ;



//===================================== HSS Added.. 20030730
#define _MAX_RELATED_MOB_				4			// ���� �ִ�� - 1 
#define _NUM_MOB_ATTACKPOSITION_		12			// ������ �����ֺ� ������ġ ���� 
#define _TERM_MOB_REGEN_				10000       // ���� ���� Ÿ�Ӱ��� 
#define _ITEM_MAX_PRICE_				100000		// ������ �ְ����� 


#define _TOTAL_CHARAC_LEVEL_			241			// ĳ������ ���� �ܰ� ( 0������ �����Ѵ�. )

#define _CHARAC_REGEN_TIME_				5000U		// �⺻ ���� ������ 5�ʴ�.
#define _CHARAC_SELF_TIME_				4000U		// ���� ä�θ� ����.

// ��ų
#ifdef _PD_SKILL_EXTEND_
	#ifdef _PD_SKILL_EXTEND_100_
		#define _COUNT_LEARN_SKILL_				(100)			// �� ĳ���Ͱ� ������ �ִ� ������ �ִ��
	#else
		#define _COUNT_LEARN_SKILL_				(80)			// �� ĳ���Ͱ� ������ �ִ� ������ �ִ��
	#endif
	#define _COUNT_BASIC_LEARN_SKILL_		(60)			// ������(Ȯ������) ���� : DB �ε� �� ������ ���� �ʿ��ϴ�.
#else
	#define _COUNT_LEARN_SKILL_				60			// �� ĳ���Ͱ� ������ �ִ� ������ �ִ��
#endif

#define _COUNT_MASTERY_SKILL_			96			// �����͸� ��ų�� ����.
#define _COUNT_MASTERY_PAGE_			4			// �����͸��� �� 3 �������� �����ȴ�.
#define _COUNT_MASTERY_PER_PAGE_		24			// �����͸��� �������� 32�� ��.
#define _COUNT_COOL_TIME_				32			// �̰� 20���� �Ѵ� ���� ���� ���̴�.
#define _INVALID_SKILL_SLOT_			0xff		// ��ȿ ��ų ���԰�����, skill add, find ���� ���� ���ϰ����� ���δ�. 
#define _CHARAC_MAX_WOUND_				30000		// �ܻ� �ִ�ġ.
#define _CHARAC_WOUND_DIVIDE_VALUE_		300			// �ܻ� �ִ�ġ�� 100���� 1.
#define _CHARAC_MAX_INSIDE_WOUND_		30000		// ���� �ִ�ġ.
#define _CHARAC_INSIDE_DIVIDE_VALUE_	300			// 
#define _CHARAC_MAX_FATIGUE_			50000U		// �Ƿε� �ִ�ġ.
#define _CHARAC_MAX_FURY_				10000		// �г� �ִ밪.
#define _FURY_CONSUME_MODE_FURY_		50			// �г� ���¿����� �Һ�
#define _FURY_CONSUME_MODE_CONV_		200			// ���� ���¿����� �Һ�
#define _FURY_ADD_PER_CRITICAL_			300			// ũ��Ƽ�� �ѹ�� 100 �� ����.
#define _TIME_USER_CHECK_PERIOD_		(300000U)	// 5�и��� üũ�Ѵ�.


// Buf, Hostile, Condition
// �̼��ڴ� 1000���� ������ ȿ�� 1��¥�� ��ų 32 ���� �������� �����̴�. 
#define _INITIAL_COUNT_EFFECT_ENTRY_		32000

#define _MAX_CHARAC_BUFF_					16			// �ִ� Buff �� ���� : 
#define _MAX_CHARAC_HOSTILE_				16			// �ִ� Hostile�� ����
#define _MAX_CHARAC_SPECIAL_BUFF_			12			// �ִ� ����� ������ ����.
#define _MAX_CHARAC_BH_ENTRY_				32			// ���� �ΰ� �� : ���� ���� ������ 

#define _MAX_CHARAC_EFFECT_ENTRY_			32			// �ܵ�� ��Ʈ�� �ִ� ����.
#define _MAX_EXTRA_EFFECT_ENTRY_			10			// ���Ƿ����� 6�� + ����Ʈ���� 4�� ��� : �Ϲ� �뵵�� ����, ���� Ȯ���� �����Ƿ�, ���� ����.

#define _MAX_CHARAC_BONUS_ADDITIVE_			12			// Ÿ�ݽ� ����� effect�� �ִ� ������ 16���̴�.(�����δ� 1 ����)
#define _MAX_ITEM_BONUS_ADDITIVE_			6			// 
#define _MAX_CHARAC_MASTERY_ADDITIVE_BONUS_	4			// �����͸��� ���� ������ ���ʽ� Additive ����.
#define	_MAX_COUNT_SKILL_ADDITIVE_			4			// ������ Additive ����.
#define	_MAX_COUNT_SKILL_GENERAL_			8			// ������ Additive ����.

#define _CONDITION_START_ID_				0			// 

#define _MAX_EFF_TIME_ERROR_				3000		// ������ ���������� ���ش�.

#define _EFFECT_INTERVAL_SECOND_			3			// 3��.
#define _EFFECT_DEF_INTERVAL_				3000U		// ���͹� ȿ������ ��� 3�ʰ� �⺻�̴�.
#define _EFFECT_DEF_INTERVAL_SEC_			(3)			// ���ǰ� �� ���� ����

#define _EFFECT_CHECK_TIME_SECOND_			20			// 20��.
#define _EFFECT_DEF_CHECK_TIME_				20000U		// �⺻ üũ �ð�. 20�ʸ��� üũ�ϰ� ������ �����Ѵ�. 

#define _PASSIVE_CEHCK_INTERVAL_			3000		// �нú� ��ų�� üũ ����. 3��.

#define _MAX_BATTLE_DISTANCE_				324			// ���� �Ÿ� 18.0f 

#define _PROB_BT_ATKRATE_					100			// ����ġ Ȯ��
#define _PROB_BT_CRITICAL_					10000		// ũ��Ƽ�� ���� Ȯ��
#define _PROB_BT_BLOCK_						10000		// ���� Ȯ��
#define _PROB_BT_BALANCE_					100			// ���� ����
#define _PERCENT_BT_FEED_					1000		// Feed �ۼ�Ʈ.
#define _PROB_BT_MABI_						10000		// ĳ������ ���� ȿ���� ������ ���̴�.


enum FURY_USE_MODE_EN
{
	fury_mode_en_normal=0,		// ���� 
	fury_mode_en_fury,			// �г� ���
	fury_mode_en_convergence	// ���� ���
} ;


/* ============================================================================
	Exception Code : throw int
	try - catch���Ͽ��� ���. throw ���� ������ ������ ���� ���� �ڵ带 ������. 
============================================================================ */
#define _EXCEPTION_CODE_DISCONNECT_			0		// ���ܰ� �߻��ϸ�, DISCONNECT �� ������ �Ѵ�. ( �α׾ƿ� ���Ѿ� �Ѵ�. )
#define _EXCEPTION_CODE_CONTINUE_			1		// ���� �����̴ϱ� �׳� �� �����Ѵ�. 
#define _EXCEPTION_CODE_INVALID_CHARAC_		2		// ĳ���� ���ÿ� ����.


/* ============================================================================
	����Ʈ ��� HHHH
============================================================================ */
#define _QUEST_COMPLETE_SUCCESS_			0x7f		// ����Ʈ �������� �ϼ� 127
#define _QUEST_COMPLETE_FAIL_				0x7e		// ����Ʈ ���з� �ϼ�   126 
#define _QUEST_CANCEL_FAIL_                 0x7d        // ����Ʈ ��� -> ���� �Ұ�. 
#define _QUEST_CANCEL_RESET_                0x00        // ����Ʈ ��� -> ���°���. ���� �غ���.
#define _MAX_CURQUEST_NUM_                  6           // ���ü��డ���� ����Ʈ �� (DB�� ����Ǵ� ����Ʈ�� ����)
#define _MAX_TEMPQUEST_NUM_					(15)		// �ӽ�����Ʈ ���� ����
#define _MAX_RUNNINGQUEST_NUM_				21			// _MAX_CURQUEST_NUM_ + _MAX_TEMPQUEST_NUM_
#define _END_OF_MAIN_QUEST_                 65535       // ���� �������� ��. 

#ifdef _PD_QUEST_MAX_600_
	#define _QUEST_1_NUMBER_					(300)		// ����Ʈ ���� Ȯ������ ����Ÿ�� ��ġ�� 2���� �и��Ǿ���.
	#define _QUEST_2_NUMBER_					(300)
	#define _QUEST_MAX_NUMBER_					(_QUEST_1_NUMBER_+_QUEST_2_NUMBER_)
	#define _QUEST_MAX_INCTEMP_					(_QUEST_1_NUMBER_+_QUEST_2_NUMBER_+_MAX_TEMPQUEST_NUM_)
#else
	#define _QUEST_MAX_NUMBER_					300		    // �������� ������ ����Ʈ ����.	-����� ���
	#define _QUEST_MAX_INCTEMP_					(_QUEST_MAX_NUMBER_ + _MAX_TEMPQUEST_NUM_)	//QuestState������ ����
#endif

#define _QUEST_KILLMOBDATA_TIME_			3000	    // ���θ��������� ��ȿ�ð� 
#define _QUEST_LASTATTACK_TIME_				3000	    // ���ݻ��� ��ȿ�ð� 
#define _QUEST_MOBKILLCHECK_MAX             5           // �� ų üũ �� �� �������� ������ MAX�� ����.

#define QUEST_AUTONODE_LIMIT				10			// �ڵ� ����̵� ����(���ѷ����� �ɸ��� �ʱ� ����)

/* ============================================================================
	
============================================================================ */
#define _XDEF_AREACATEGORY_SAFE				0			// ���� ���� 
#define _XDEF_AREACATEGORY_PLAYERSPAWN		1			// ĳ���� ������
#define _XDEF_AREACATEGORY_MOBSPAWN			2			// ���� ������
#define _XDEF_AREACATEGORY_NPCAREA			3			// NPC ����
#define _XDEF_AREACATEGORY_QUESTAREA		4			// ����Ʈ�� ���̴� ����
#define _XDEF_AREACATEGORY_EVENTAREA		5			// �̺�Ʈ�� ���̴� ����
#define _XDEF_AREACATEGORY_MAPWARP			6			// ���� ����
#define _XDEF_AREACATEGORY_PVPAREA          7			// ���� ����
#define _XDEF_AREACATEGORY_CASTLE			8			// ��� ����
#define _XDEF_AREACATEGORY_CANNOTLOGOUT		9

#define _XDEF_AREACATEGORY_COUNT			10			// ���� ī�װ��� ����

// ���� ������������ ����.
#define _XDEF_AREASHAPETYPE_BOX             0			// 
#define _XDEF_AREASHAPETYPE_SPHERE          1			//





//#################################################################################
// Ÿ�̸� ���
//###################################################################### LHS Added....

/* ============================================================================
	ĳ���� Ÿ�̸� �޼���.
============================================================================ */
enum {
	_TM_CH_CHECK_STATE_ = 0,	// 0 ĳ���� �������� üũ : 10 ��
	_TM_CH_LOGOUT_,				// 1 �α� �ƿ� ���Ѷ�.	1
	_TM_CH_RESTART_,			// 2 ĳ�� ����� ���� �¾ ��ġ�� ������� 
	_TM_CH_REGEN_,				// 3 ���� �޽���
	_TM_CH_ADD_JIN_,			// 4 ����ġ �߰� �޽���
	_TM_CH_SEND_CHARAC_INFO_,	// 5 ĳ������ ������ �κ������� ������.  5
	
	_TM_CH_CALC_FATIGUE_,		// 6 �Ƿε� ����� �϶�. 

	_TM_CH_ATK_TIMER_,			// ĳ����, ä�θ�, ���� ���� ���� ���� ���� ��Ÿ���� Ÿ�̸�
	_TM_CH_ROUNDING_FIRE_,		// ���� ������ ��û�Ǿ���, ���� ������ ������ �ð��̴�. : 2004/08/18
	_TM_CH_CASTING_FIRE_,		// ĳ���� ������ ��û�Ǿ���, ���� ������ ������ �ð��̴�. : 2004/08/18
	_TM_CH_SELF_SKILL_,			// 10 Self Skill ����� : ����� ���� �� ó��.
	_TM_CH_SELF_SKILL_CANCEL_,	// self skill �� ���� �Ǿ��� ���, 1�ʰ��� �����ð��� �ʿ��Ѵ�. ( �ִϿ��̼� )  11

	_TM_CH_REQ_SV_MOVE_,		// ���� �̵��� üũ. ���и� �׳� �α׾ƿ�. �ٸ����� ���� ����. 
	_TM_CH_REQ_SV_2_,
	
	_TM_CH_BINSA_SEND_OTHERS_,	// ������ �����¿� ������ ���� �˸���.
	_TM_CH_BINSA_,				// 15 �����¿� ������. ���� �Ŀ� �ٷ� ��ϵȴ�. 
	_TM_CH_TIMEOUT_BINSA_,		// ȥ�� ���¿� ����. ��� ���°� Ÿ�Ӿƿ��� ����
	_TM_CH_RESERVE_BINSA_AWAKE_,// ��翡�� �츱�� ������.  16
	_TM_CH_RESERVE_HONSU_AWAKE_,// ȥ�� ���¿��� �츱 �� ������.
	
	_TM_CH_DEAD_BY_PK_,			// pk �� ���� �� �׾����� ������ �˸���.

	_TM_CH_DEAD_IN_MATCH_,		// 20 ���� ����ߴٰ� ������ �˸� �� ����.
	_TM_CH_MATCH_BINSA_,		// 21 ���� ���ó���Լ�( Timeout Binsa �� �ȺҸ��� ��찡 ���� )
	
	_TM_CH_DEAD_IN_FM_,			// ������(�񹫴�ȸ) �� ����ߴٰ� ������ �˸� �� ����.
	_TM_CH_FM_BINSA_,			// ������(�񹫴�ȸ) �� ���ó�� �Լ�.
	_TM_CH_FM_AWAKE_,			// ������ �� ���� ��, ��Ȱ��ų ��.
	_TM_CH_FM_LEAVE_,			// 25 �����񹫰� ������. ���忡�� ��������.
	
	_TM_CH_RELEASE_POINTER_,	// �ڽ��� �����͸� ���� ��ȯ�Ѵ�. 
	
	_TM_CH_DISCONNECT_,			// 27 ����
	_TM_CH_PARTY_INFO_,			// ��Ƽ ���� ��û
	_TM_CH_PARTY_CONFIRM_,		// �̶����� ��Ƽ ������ ���ٸ�, ��Ƽ ������ �ʱ�ȭ ��Ű��, Ż�� �˸���. 26
	_TM_CH_CHECK_PARTY_,	

	_TM_CH_COUNT_USER_,			// 30 ���� �������� ������ ���� DS�� ������. �⺻���� 2�и��� �ѹ���.
	_TM_CH_PARTY_MOVE_COMPLETE,	// ��Ƽ ����� ���� �̵� �Ϸ� ��Ŷ�� ������..(�����̵� �������� ���)
	_TM_CH_UPDATE_MATCH_,		// 32 �� ���� ������Ʈ �ñ�.

	_TM_CH_SEND_SKILL_ADD_,			// ����/�Ͻ� �߰�
	_TM_CH_SEND_ENTRY_ADD_,			// ȿ�� �߰�.
	
	_TM_CH_CHECK_CONDITION_TIMER_,	// 35 �ܵ���� �ð��� üũ�Ѵ�. ���͹��� �͵��� 3�� ����, �˻�, �ƴϸ� �ð� �˻�.
	_TM_CH_APPLY_CONDITION_,		// 3�ʸ��� �ѹ��� ���� �ܵ�� üũ Ÿ��.

	_TM_CH_CHECK_EXTRA_CON_TIMER_,	// 37
	_TM_CH_APPLY_EXTRA_CON_,		// 
	
	_TM_CH_SEND_SKILL_DEL_,			// ���� ����.
	_TM_CH_SEND_ENTRY_DEL_,			// 40 ȿ�� ����.

	_TM_CH_CHECK_CONNECT_,			// ���� ��, ������ ���������� �̷�������� �˻�.

	_TM_CH_GET_MY_ORGANIZATION_INFO,	// 42 ���� ������ �о�´�.


	_TM_CH_PASSIVE_CHECK_,			// �нú� ��ų�� üũ


	_TM_CH_CHECK_USER_TIMER_,		// ���� Ÿ�̸Ӹ� �˻��Ѵ�. 

	_TM_INTOX_USER_TIMER_,          // 45 ���� �ߵ� ����.

	_TM_NIC_USEFULL_TIME_CHECK_,
	
	_TM_CHECK_SKILL_BOOK_,			// 47 ������ ���� ����
	
	_TM_ZONE_EFFECT_,				// ���� ���� ��, Ư�� ������ ����Ǵ� ����ȿ��.
	_TM_EVENT_MOB_EXECUTE,			// �̺�Ʈ ������ ����. �ӽ� �ڵ�
	
	_TM_MOBKILL_MSG,				// 50 ���� ų �޼���.
	_TM_PK_EVENT__NOTICE,			// PKEVENT ���� ���
	_TM_PK_EVENT__START,			// 52 PKEVENT ����
	_TM_PK_EVENT__TIMEOUT,			// PKEVENT Ÿ�Ӿƿ�
	_TM_PK_EVENT__SPAWN,			// �����屺 ���� �ð� ^^ : 20070709 Ray.
	_TM_PK_EVENT__DESTROY,			// 55 �����屺 ���� �ð�.

	_TM_AIUS_GRADE_CHECK,			//  Grade Item �ð� üũ.

	_TM_USER_FATIGUE_WEIGHT_CLOCK_, // 57 1�и��� FatigueWeight �̺�Ʈ �߻�
	
	_TM_USER_PITEM_CHECK_,			// �������� �Ⱓ�� ������ üũ
	_TM_USER_PITEM_TEMP_CHECK_,		// �ӽ����� �Ⱓ�� ������ üũ
	
	// M-etel GameGuard ��������
	_TM_GGAUTH_VALIDATION,			// 60 
	_TM_GGAUTH_RESERVATION,			// 

	_TM_SERVER_DOWN_,
	
	_TM_CASTLE_EVENT_START_,		// 62 ����� : �غ� �ܰ���� ���۽�Ų��. �غ� �ܰ�.( ���� �ܰ谡 �ƴ� )
	_TM_CASTLE_NEXT_STEP_,			// ����� : ���� �ܰ�� ����.

	_TM_CASTLE_TIME_CHECK_,			// ����� : ���� �ð�(�غ� �ܰ�)�� �ľ��Ͽ�, _TM_CASTLE_EVENT_START_ ��Ŷ�� ������.
	_TM_CASTLE_RESET_ENEMY_,		// 65 ����� : ���� ���� ����.

	_TM_CASTLE_POINT_ADD_,			// ����� : ������ ���̸� ����� +1.
	_TM_CASTLE_PERIOD_TIMER_,		// 67 ����� : �ֱ������� �ؾ��� �ϵ� ó��.
	
	_TM_CASTLE_TAX_,				// ������ value �� ó��. 0 : req_start_compute, 1 : req_total_tax

	_TM_USER_LTS_POS_LIST_CLOCK_,	// ��ġ���� : 3�ʸ��� ��ġ���� ����.
	
	_TM_SEND_RANKING_LIST_WHEN_COMBAT_END,	// 70 _TM_SEND_AWORD_WHEN_COMBAT_END_, // ������� ������ 3�� �Ŀ� ���� ��ŷ �߼� 
	
	_TM_SEND_AWORD_FOR_RANK_AND_PVP_,		// ��ŷ ����Ʈ�� ������ 10�� �Ŀ� ������.
	
	_TM_RF_FORCE_MOVE_,				// 72 ģ�� ��ȯ : ���� �̵���Ų��.
	
	_TM_CH_SEND_ENTRY_DEL_382_,		// 382�� +all stat 10 �� �� �̿��� �� ���� ���� ���� 
	
	_TM_CHECK_DS_COM_,				// DS ��� �׽�Ʈ.
	
	_TM_USER_END_BONUS_HYPERRUN_,	// ���ʽ� ����� ����.
	
} ;

//
#define _TM_COMMON_CONSTAT					-1			// 

#define _CHECK_TIME_SVMOVE_					(10000U)		// ���� �̵� üũ �ð� 5��.
#define _DISCONNECT_TIME_AFTER_SVMOVE_		(2000U)
#define _CHECK_TIME_AIUS_GRADE_				(60U*60U)

/* =====================================================================================
	Skill �� ���ų� �����Ű�� ���ϴ� ���� : 2004/05/12 Ray
===================================================================================== */
enum
{
	_SKILL_CAN_LEARN_SUCCESS_ =	0,		// ���� �ִ�.
	_SKILL_CAN_LEARN_CONTRIBUTION_,		// �⿩�� ����.
	_SKILL_CAN_LEARN_LEVEL_, 			// ���� �ܰ� ���ڸ�.
	_SKILL_CAN_LEARN_PRE1_,				// ���� ���� 1 ����.
	_SKILL_CAN_LEARN_PRE2_,				// ���� ���� 2 ����. 
	_SKILL_CAN_LEARN_CLAN_,				// ���� ���� �ȸ���.
	_SKILL_CAN_LEARN_SKILL_INDEX_,		// id�� �ش��ϴ� ��ų�� ����.
	_SKILL_CAN_LEARN_SKILL_FULL_,		// ���̻� ��ų ������ ����.
	_SKILL_CAN_LEARN_SKILL_ALREADY_,	// �̹� ������� �ִ�. 
	_SKILL_CAN_LEARN_NO_MONEY_,			// ���� ����. 
	_SKILL_CAN_LEARN_STEP_FULL_,		// �ܰ谡 12���� �����ߴ�.
	_SKILL_CAN_LEARN_MORE_SKILLEXP_,	// ��ų ����ġ�� �� �ʿ��ϴ�.
	_SKILL_CAN_LEARN_GRADE_,			// ��å ����� ���ڸ���.
} ;

#define _MAX_EFFECT_PER_SKILL_				(8)
#define _MAX_ADDITIVEEFFECT_PER_SKILL_		(4)

/*===================================================== HSS Modified 2003.0808
���Ϳ� ���� ����� 
===============================================================================*/	
//========= ���� ���ݰŸ�Ÿ�� ==========//
enum ATTACK_TYPE{
	short_attack = 0,	//�ٰŸ�
		long_attack			//���Ÿ� 
};

//========= Ÿ�� ���� ��ȣ�� =============//
enum TARGET_TYPE{
		PREFER_NONE = 0,		// 0 : �⺻ ������
		PREFER_NORMAL,			// 1 : 1ȸ ������
		PREFER_RANDOM,			// 2 : ����.
		PREFER_DISTANCE,		// 3 : �Ÿ� �켱
		PREFER_CLASS_JI,		// 4 : ������ �켱.
		PREFER_CLASS_GI,		// 5 : ����� �켱.
		PREFER_CLASS_COMP,		// 6 : ������ �켱.
		PREFER_CLASS_COMB,		// 7 : ������ �켱.
		PREFER_FIRST,			// 8 : ó�� ģ ���.
};

//========= �̵��� ���ð� Ÿ�� =============//
enum MOVE_DELAY_TYPE{
	more_static = 0,
		normal_static,
		dynamic
};

//========= ������ ������ ���ؼ� ============//
enum BATTLE_TYPE{
	brave = 0,
		weakness1,
		weakness2,
		cunning1,
		cunning2,
		weakest		// �� ����. �׷��� ������ ����ģ��.
} ;

#define MONSTER_DELAY_TIME			4000U			// �⺻ ������
#define MONSTER_DO_NOTHING_TIME		1000U			// ��� ��ȯ�� ��� ������ �ð�.
#define MONSTER_AFTER_DIE			1000U			// ���� �װ�, Exp�� Item�� ���� �� ������ �ð���
#define MONSTER_RESPONSE_TIME		180U			// �ٸ� �ൿ��, �̺�Ʈ�� �޾��� �� ���� �ð�.
#define MONSTER_IMMEDIATE_TIME		50U				// �ٷ� �ٸ� �ൿ�� ���� ���� ���� �ð�.
#define MONSTER_TARGETTING_TIME		7000U			// ���� Ÿ���� �⺻ ����.
#define MONSTER_EXP_VALID_TIME		120000U			// ����ġ�� ��ȿ�� �ð�.

//////////////
//Rank Define
#define DNPC_RANK_GENERAL		(0)
#define DNPC_RANK_NAMED			(1)
#define DNPC_RANK_RAID			(2)
#define DNPC_RANK_HOBUB			(3)	//ȣ��
#define DNPC_RANK_BOSS			(4)
#define DNPC_RANK_ETC			(5)
#define DNPC_RANK_EVENT_WHITE	(6)		// �̺�Ʈ ����
#define DNPC_RANK_EVENT_BLACK	(7)		// �̺�Ʈ ����
#define DNPC_RANK_EVENT_GEN_BOSS	(8)	// �� ���屺�� ���� ��ũ.

//���簨 POINT
#define BASIC_ATTACKPOINT			(100)
#define ATTACKPOINT_PER_LEVEL		(1)

//Random PathID
#define PATH_ID_RANDOM				(10000)

//===========
//enum 

/* ============================================================================
	��Ÿ Ÿ�̸� �޽���
============================================================================ */
#define _TM_CPU_LOG_			60000		// pUser�� �ݵ�� NULL �̾�� �Ѵ�. 



/* =====================================================================================
	��Ƽ Ÿ�̸� �޽���
===================================================================================== */
enum {
	_TM_PT_CHECK_CREATE_,		// ���� üũ ( ���̵� ������ -1 ���� �˻��Ѵ�. )
	_TM_PT_RELEASE_,			// ������ ��ȯ : ���ķδ� ������ ����� ��.
	_TM_PT_BREAKUP_,			// �׼��������� �̻����� ��ü ( ���� ��ü�� CS �޽��� ó���κп��� )
	_TM_PT_CHECK_SLOT_,			// üũ( ��ġ������ ����ġ ������ )
	_TM_PT_ERASE_FROM_MAP_,		// �ʿ��� �����Ѵ�.
	_TM_PT_USER_CHECK_,			// ���� ������ ������ ���¸� �˻��Ѵ�. 
	_TM_PT_SEND_MEMBER_,		// ��Ƽ������ ���� ����( ü��, ����... )�� ������. 
	_TM_PT_SEND_PARTY_INFO_,	// ��Ƽ������ �ٽ� �����ش�.	
	_TM_PT_REQ_PARTY_INFO,		// CS �� ��Ƽ ���� ��û
	_TM_PT_MOVE_COMPLETE,		// ���� �̵� �Ϸ� ����
	_CS_CONNECTION_RESTORE,		// CS ������ �����Ѵ�.	ozzywow add
	_TM_PT_CLOSE_THREAD			// ������ ���� �޽���.
} ;


/* =====================================================================================
	������, ���� (��������, �̴ϰ���)
===================================================================================== */
// �� Ÿ�̸�
enum {
	_TM_BATTLE_ROOM_STAGE_CHECK,
	_TM_BATTLE_ROOM_DESTORY,
	_TM_TRAINING_ROOM_DESTROY,
	_TM_BATTLE_ROOM_MEM_RELEASE,	
	_TM_BATTLE_ROOM_CREATE_CHECK,
	_TM_BATTLE_ROOM_TIME_SEND,
	_TM_BATTLE_ROOM_END_,
};

#define _COUNT_EX_BATTLE_ROOM_TYPE_MAX		12	// �� ���� ������ �� ������ �ִ� ����..
#define _COUNT_EX_BATTLE_ROOM_REGION_MAX	256	// �� �ȿ� �������� ����..

// ĳ�� ���� �÷���
enum {
	_FLAG_EX_BATTLE_NOTHING_,				// ������ ���������� ����
	_FLAG_EX_BATTLE_READY,					// ������ ������ ���� �����..
	_FLAG_EX_BATTLE_ING,					// ������ ������...
};



/* =====================================================================================
	
===================================================================================== */

enum item_dur_decrease_info
{
	dur_mode_none=0,
	dur_mode_atk_normal,		// �ڽ��� ������ �������� ��
	dur_mode_atk_critical,		// ġ��Ÿ ���� ����
	dur_mode_atk_spell,			// ����� ���� ���ݽ�
	
	dur_mode_def_block,			// ��� ����
	dur_mode_def_normal,		// �븻 �������� �Ծ��� ��
	dur_mode_def_cri_damage,	// ġ��Ÿ ����.

	dur_mode_ab_avoid,			// ��-ȸ�� �Ǵ� ���� �� : �������� ���� �ʱ� ���� �߰��Ѵ�. 20050302
	
	dur_mode_item_upgrade,		// ����
	
	dur_mode_binsa,				// ��� ���¿� ���� ��
	dur_mode_honsu				// ȥ�� ���¿� ���� ��.
} ;


enum		// ĳ������ 
{
	en_charac_mode_peace=0,			// ��ȭ ��� ����
	en_charac_mode_battle=1,		// ���� ��� ����.
	en_charac_mode_pk=2,			// pk ��� ����.
} ;


#define _PK_COUNT_MAX_				(600U)		// Max PK Count
#define _PK_COUNT_PER_KILL_			(60U)		// Add count when kill user
#define _PK_COUNT_PER_ATTACK_		(20U)		// Add count when attack user
#define _PK_COUNT_PER_DEFENCE_		(5U)		// Add count when defence


#define IF_MOBDEBUG				if( g_bDebug[debug_monster] )
#define IF_SKILLDEBUG			if( g_bDebug[debug_skill] )
#define IF_BATTLEDEBUG			if( g_bDebug[debug_battle] )
#define IF_ITEMDEBUG			if( g_bDebug[debug_item] )
#define IF_CHARACDEBUG			if( g_bDebug[debug_charac] )
#define IF_SUBBATTLEDEBUG		if( g_bDebug[debug_subbattle] )
#define IF_WOUND_DEBUG			if( g_bDebug[debug_wound] )
#define IF_QUEST_DEBUG			if( g_bDebug[debug_quest] )
#define IF_PARTYDEBUG			if( g_bDebug[debug_party] )
#define IF_ALLYDEBUG			if( g_bDebug[debug_ally] ) 
#define IF_MATCHDEBUG			if( g_bDebug[debug_match] )
#define IF_UNIONDEBUG			if( g_bDebug[debug_union] )
#define IF_GGUARDDEBUG			if( g_bDebug[debug_gguard] )

// �ͼ��� ó������.
#undef TRY
#undef CATCH_ALL

#define TRY					__try
#define CATCH_ALL			__except(g_pNDExceptionReport->GenerateExceptionReport(GetExceptionInformation()),EXCEPTION_EXECUTE_HANDLER)
#define FINALLY             __finally

/* =====================================================================================
	���� ( �Ǵ� ������Ʈ ) �������� �μ�
===================================================================================== */
#define _RESTORE_MONSTER_INFO_			1	    // ���� ������ �ٽ� �ε��϶�.

/* Ʈ���̵� ���� ����. HHH */ 
#ifdef _PD_COMPANY_NXINTS_
	#define _MAX_TRADE_SLOT_                20     // �ִ� Ʈ���̵� ���� ����  
	#define _MAX_TRADE_ITEMSLOT_            50     // �ִ� Ʈ���̵� ������ ���� ����
#else
	#define _MAX_TRADE_SLOT_                1000     // �ִ� Ʈ���̵� ���� ����  
	#define _MAX_TRADE_ITEMSLOT_            5000     // �ִ� Ʈ���̵� ������ ���� ����
#endif

/* ������ ��ε�ĳ���� ���� HHH */ 
#define _MAX_ITEM_BROADCAST_NO_         80       // �� ������ ��ε�ĳ���� ��Ŷ�� ���� �� �ִ� ������ ����.
#define _ITEM_DELETE_TIME_FROM_WORLD_   300000   // ������ ��ӵ� ���� ���忡�� �������� �Ǵ� �ð�. 5������ ����.



/* =====================================================================================
// ȿ�� Ư�� - ��Ʈ �÷��� : Effect Property : 15�� ���� ����.
===================================================================================== */
#define _EFFECT_PROP_NONE_					(0)
#define _EFFECT_PROP_GENERAL_				(0x01)				// �����ð� ���� ����	: ����ġ
#define _EFFECT_PROP_REFLECTION_			(0x01<<1)			// ���� �ݻ� ����
#define _EFFECT_PROP_FEED_					(0x01<<2)			// ���ݽ� �������κ��� ��������.
#define _EFFECT_PROP_INITIAL_ONCE_			(0x01<<3)			// ���� ������ �ѹ� ����ǰ� ��.
#define _EFFECT_PROP_ONLY_ONCE_				(0x01<<4)			// �ѹ� �����ϸ�, �����.( ��ų�� �Բ� )
#define _EFFECT_PROP_APPLY_ADDITIVE_		(0x01<<5)			// ���ݽ� ���濡�� �߰��� ��. : �̰�, 
#define _EFFECT_PROP_BONUS_ADDITIVE_		(0x01<<6)			// ���ݽ� ���濡�� �߰��� ��. : �̰�, 

#define _EFFECT_PROP_ABNORMAL_				(0xff)


#define IsGeneralProp(x)		( x & _EFFECT_PROP_GENERAL_ )
#define IsReflectionProp(x)		( x & _EFFECT_PROP_REFLECTION_ )
#define IsFeedProp(x)			( x & _EFFECT_PROP_FEED_ )
#define IsInitialOnceProp(x)	( x & _EFFECT_PROP_INITIAL_ONCE_ )
#define IsOnlyOnceProp(x)		( x & _EFFECT_PROP_ONLY_ONCE_ )
#define IsApplyAdditiveProp(x)	( x & _EFFECT_PROP_APPLY_ADDITIVE_ )
#define IsBonusAdditiveProp(x)	( x & _EFFECT_PROP_BONUS_ADDITIVE_ )

/*
	���� Ȯ�� �� : ����Ʈ ��Ʈ���� ���� ��ȣ�� ���δ�. ( m_ExtraEffect )
	���� Ȯ�� �� : ����� ������ ���� ��ȣ�� ���δ�. ( m_SpecialBuff )
*/
#define	_NPCBUFF_EFFECT_SLOT_MONEY_		0	// NPC ���� ���� : Money
#define _NPCBUFF_EFFECT_SLOT_PCROOM_	1	// NPC ���� ���� : �Ǿ��� 
#define _NPCBUFF_EFFECT_SLOT_PERSONAL_	2	// NPC ���� ���� : ����


//  Feed Ư���� ��, ��𿡼� ������.
enum EN_EFFECT_FROM_TARGET
{
	en_eff_from_none=0,				// �⺻ �Ӽ�.
	en_eff_from_hit,				// �� ���������� 
	en_eff_from_life,				// ������ ü�¿���.
	en_eff_from_force,				// ������ ���¿���
	en_eff_from_concentration		// ������ ��������
} ;



/* =====================================================================================
// Effect Value Property : 0 - Value. 1 - Percent.
===================================================================================== */
enum EN_EFFECT_VALUE_TYPE
{
	en_eff_vt_add_value=0,		// value �� �״�� ����
	en_eff_vt_add_percent,		// Percent �� ����
	en_eff_vt_sub_value,		// value �� �״�� ����
	en_eff_vt_sub_percent		// Percent �� ����
} ;


// ��� �������� ��� Ÿ���� ��ȣ.
enum EN_EFFECT_WHERE_APPLY
{
	en_eff_where_none=0,						// 0 ���� �����ؼ� �ִ� 255...
		
	en_eff_where_attack_rate,					// ���� ����ġ : 'attack_rate'
	en_eff_where_avoid_rate,					// ȸ�� ����ġ : 'avoid_rate'
	en_eff_where_attack_damage,					// 3 ���ݷ�
	en_eff_where_defence, 						// 4 ����
	en_eff_where_defence_rate,					// 5 ��� Ȯ�� : 1000����.

	en_eff_where_m_attack_rate,					// 6 ��� ���� ����ġ
	en_eff_where_m_avoid_rate,					// 7 ��� ȸ�� ����ġ
	en_eff_where_m_attack_damage,				// 8 ��� ���ݷ�
	en_eff_where_m_defence,						// 9 ��� ����
	en_eff_where_m_defence_rate,				// 10 ��� ��� Ȯ��
	
	en_eff_where_life,							// 11 ������
	en_eff_where_force,							// 12 ����
	en_eff_where_concentration,					// 13 ����

	en_eff_where_max_life,						// 14 �ִ� ������(ü��)
	en_eff_where_max_force,						// 15 �ִ� ����
	en_eff_where_max_concentration,				// 16 �ִ� ���� 

	en_eff_where_strength,						// 17 �ٷ� ���ʽ�
	en_eff_where_zen,							// 18 ����
	en_eff_where_intelligence,					// 19 ����
	en_eff_where_constitution,					// 20 �ǰ�
	en_eff_where_dexterity,						// 21 ��ø


	en_eff_where_critical_rate,					// 22 ġ��Ÿ Ȯ�� ����.
	en_eff_where_cri_avoid_rate,				// 23 ġ��Ÿ ȸ�� Ȯ�� ����
	en_eff_where_ref_p_damage,					// 24 ���� ������ �ݻ�
	en_eff_where_ref_m_damage,					// 25 ��� ���ݷ� �ݻ�
	en_eff_where_ref_hon_damage,				// 26 ȥ�� ������ �ݻ�
	en_eff_where_ref_sin_damage,				// 27 ���� ������ �ݻ�

	en_eff_where_add_minus_damage,				// 28 ���� ������ �߰�
	en_eff_where_add_plus_damage,				// 29 ���� ������ �߰�
	en_eff_where_add_hon_damage,				// 30 ȥ�� ������ �߰�
	en_eff_where_add_sin_damage,				// 31 ���� ������ �߰�

	en_eff_where_wound_out,						// 32 �ܻ�
	en_eff_where_wound_in,						// 33 ����
	
	en_eff_where_regen_life,					// 34 �ڵ� ���� : ����
	en_eff_where_regen_force,					// 35 �ڵ� ���� : ����
	en_eff_where_regen_concentration,			// 36 �ڵ� ���� : ����
	
	en_eff_where_resist_minus,					// 37 ���� ���׷�
	en_eff_where_resist_plus,					// 38 ���� ���׷�
	en_eff_where_resist_hon,					// 39 ȥ�� ���׷�
	en_eff_where_resist_sin,					// 40 ���� ���׷�
	
	en_eff_where_force_defence,					// 41 ���� ��� : apply_additive
	en_eff_where_concentration_defence,			// 42 ���� ��� : apply_additive
	
	en_eff_where_give_life,						// 43 �ڽ��� ���� ����
	en_eff_where_give_force,					// 44 �ڽ��� ���� ����
	en_eff_where_trans_force_to_life,			// 45 �������� ������ �������� ��ȯ
	en_eff_where_trans_life_to_force,			// 46 �������� ������ �������� ��ȯ	

	en_eff_where_incre_sk_mabi_prob,			// 47 ���� : ���� Ȯ�� ����.
	
	en_eff_where_add_condition,					// 48 ���¸� �߰���Ŵ.
	en_eff_where_remove_condition,				// 49 ���¸� �����Ѵ�.
	en_eff_where_remove_spell=50,				// 50 ������ �����Ѵ�.
	en_eff_where_move_speed,					// 51 �̵� �ӵ� % ����/����
	en_eff_where_attack_speed,					// 52 ���� �ӵ� % ����/����
	en_eff_where_absolute_avoid,				// 53 ��� ������ nȸ ȸ���Ѵ�.
	en_eff_where_absolute_reflect,				// 54 ��� ������ nȸ �ݻ��Ѵ�.
	en_eff_where_absolute_attack,				// 55 ��� ������ nȸ �׻� ����
	en_eff_where_absolute_critical,				// 56 ���� ������ nȸ ġ��Ÿ ����
	
	en_eff_where_feed_life,						// 57 ������ ������ ���Ѵ´�. : initial once
	en_eff_where_feed_force,					// 58 ������ ������ ���Ѵ´�. : initial once
	en_eff_where_feed_concentration,			// 59 ������ ������ ���Ѵ´�. : initial once

	en_eff_where_ref_minus_damage,				// 60 ���� ������ �ݻ�
	en_eff_where_ref_plus_damage,				// 61 ���� ������ �ݻ�
	
	
	en_eff_where_add_minus_spell_dam,			// 62 ���� ����� �߰� �������� �ش�. general, apply_additive
	en_eff_where_add_plus_spell_dam,			// 63 ���� ����� �߰� �������� �ش�. general, apply_additive
	en_eff_where_add_hon_spell_dam,				// 64 ȥ�� ����� �߰� �������� �ش�. general, apply_additive
	en_eff_where_add_sin_spell_dam,				// 65 ���� ����� �߰� �������� �ش�. general, apply_additive
	en_eff_where_shield_skill_atk,				// 66 ���� - ����. ������ n ��ŭ ����. apply_additive
	en_eff_where_shield_spell_atk,				// 67 ���� - ���. ������ n ��ŭ ����. apply_additive
	en_eff_where_shield_minus_atk,				// 68 ���� - ��. ������ n ��ŭ ����. apply_additive
	en_eff_where_shield_plus_atk,				// 69 ���� - ��. ������ n ��ŭ ����. apply_additive
	en_eff_where_shield_hon_atk,				// 70 ���� - ȥ. ������ n ��ŭ ����. apply_additive
	en_eff_where_shield_sin_atk,				// 71 ���� - ��. ������ n ��ŭ ����. apply_additive
	en_eff_where_shield_all_atk,				// 72 ���� - ���. ������ n ��ŭ ����. apply_additive

	en_eff_where_all_stat,						// 73 ��� ���� ����/����.

	en_eff_where_weapon_dam,					// 74 ������ ������ ����/����.
	
	en_eff_where_rebirth_target,				// 75 ��Ȱ ��Ų��.

	en_eff_where_weapon_max_dam,				// 76 ���� �ִ� ������ ����/����
	en_eff_where_weapon_min_dam,				// 77 ���� �ּ� ������ ����/����
	
	en_eff_where_item_drop_percent,				// 78 ������ ��� Ȯ�� ����
	
	en_eff_where_balance,						// 79 ���� ������ ����/����
	en_eff_where_cri_dam_bonus,					// 80 ġ��Ÿ ���ݷ� % ����.( %�� �ִ�. )
	
	en_eff_where_sk_target_modify,				// 81 ������ Ÿ�� Ÿ�� ����
	en_eff_where_sk_effectapply_modify,			// 82 ������ ���� Ÿ�� ����
	en_eff_where_sk_angle_modify,				// 83 ������ ���� �Ӽ� ����
	en_eff_where_sk_fera_modify,				// 84 ������ ���� �Ӽ� ����
	en_eff_where_sk_ferp_modify,				// 85 ������ ��� �Ӽ� ����
	
	en_eff_where_sk_distance,					// 86 ������ distance ����/����
	en_eff_where_sk_radius,						// 87 ������ radius ����/����
	
	en_eff_where_sk_min_dam,					// 88 ������ �ּ� ������ ����/����
	en_eff_where_sk_max_dam,					// 89 ������ �ִ� ������ ����/����
	
	en_eff_where_sk_attackrate,					// 90 ������ ����ġ ����/����
	en_eff_where_sk_avoidrate,					// 91 ������ ȸ�� ����/����( �ʽı⿡���� �ǹ̰� ���� )
	
	en_eff_where_sk_costinner,					// 92 ������ �Ҹ� ���� ����
	en_eff_where_sk_costmind,					// 93 ������ �Ҹ� ���� ����
	en_eff_where_sk_finishspeed,				// 94 �ǴϽ� ���ǵ� ����
	
	en_eff_where_sk_castingtime,				// 95 ĳ���� ����
	en_eff_where_sk_delay,						// 96 ���� ������ ����
	en_eff_where_sk_cooldown,					// 97 ���� ��ٿ� ����

	en_eff_where_sk_incre_efftime,				// 98 ������ �ð� ����
	en_eff_where_sk_additive_1_time,			// 99 ������ ����ȿ�� 1 ���� �ð� ����
	en_eff_where_sk_additive_1_value=100,		// 100 ������ ����ȿ�� 1 �� ����
	en_eff_where_sk_additive_1_prob,			// 101 ������ ����ȿ�� 1 Ȯ�� ����
	en_eff_where_sk_additive_2_time,			// 102 ������ ����ȿ�� 2 ���� �ð� ����
	en_eff_where_sk_additive_2_value,			// 103 ������ ����ȿ�� 2 �� ����
	en_eff_where_sk_additive_2_prob,			// 104 ������ ����ȿ�� 2 Ȯ�� ����
	en_eff_where_sk_additive_3_time,			// 105 ������ ����ȿ�� 3 ���� �ð� ����
	en_eff_where_sk_additive_3_value,			// 106 ������ ����ȿ�� 3 �� ����
	en_eff_where_sk_additive_3_prob,			// 107 ������ ����ȿ�� 3 Ȯ�� ����
	en_eff_where_sk_additive_4_time,			// 108 ������ ����ȿ�� 4 ���� �ð� ����
	en_eff_where_sk_additive_4_value,			// 109 ������ ����ȿ�� 4 �� ����
	en_eff_where_sk_additive_4_prob,			// 110 ������ ����ȿ�� 4 Ȯ�� ����

	en_eff_where_incre_sk_mabi_time,			// 111 ���� ���� �ð� ����
	en_eff_where_incre_sp_mabi_prob,			// 112 ��� ���� Ȯ��
	en_eff_where_incre_sp_mabi_time,			// 113 ��� ���� �ð� 
	en_eff_where_m_balance,						// 114 ���  ������ ����/����
	en_eff_where_m_critical_rate,				// 115 ��� ġ��Ÿ Ȯ�� ����.
	en_eff_where_m_cri_avoid_rate,				// 116 ��� ġ��Ÿ ȸ�� Ȯ�� ����
	en_eff_where_p_critical_rate,				// 117 ���� ġ��Ÿ ȭ�֤� ����
	en_eff_where_p_cri_avoid_rate,				// 118 ���� ġ��Ÿ ȸ�� ����
	en_eff_where_monster_taunt,					// 119 ���� Ÿ��Ʈ :
	en_eff_where_monster_detaunt,				// 120 ���� ��Ÿ��Ʈ
	en_eff_where_monster_hate,					// 121 ���� ����Ʈ
	en_eff_where_p_balance,						// 122 ���� �߶���.
	en_eff_where_max_life_and_force,			// 123 ������ ���� ��� : general, apply

	// 2005/12/27 Added
	en_eff_where_disable_skill,					// 124 Ư�� ���� ��� �Ұ�. general, apply
	en_eff_where_disable_s_type,				// 125 Ư�� s type ���� ��� �Ұ�; general, apply
	en_eff_where_cancel_current,				// 126 ���� �������� ������ ��� ��Ų��. : initial
	en_eff_where_cancel_hyperrun,				// 127 �������� ����� ��� ��Ų�� : initial
	en_eff_where_destroy_force,					// 128 ���� �ı� : initial
	en_eff_where_resist_prob_for_mabi,			// 129 ���� ���� Ȯ�� _PROB_BT_MABI_ : general, apply
	en_eff_where_delete_all_buff,				// 130 ��� ���� ���� : 2006/02/01 : initial

	en_eff_where_resistance_poison_pk,			// 131 �пյ� ���� ���� : 2006/02/27 : only apply & food - 5 
	en_eff_where_no_effect,						// 132 �ƹ� ȿ�� ���� �����ܸ� ����.
	
	// 2006.04.10 molla >>>>>>>>>>>>>>>>>>>>>>>>>
	en_eff_where_fury,							// 133 �г� ��ġ ����.
	en_eff_where_remove_buff,					// 134 debuff n��
	en_eff_where_remove_hostile,				// 135 dehostile n��
	//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
	en_eff_where_sk_feed_life,					// 136	// ������ ���� : feed
	en_eff_where_sk_feed_force,					// 137	// ������ ���� : feed
	en_eff_where_sk_feed_concentration,			// 138	// ������ ���� : feed
	en_eff_where_sp_feed_life,					// 139	// ����� ���� : feed
	en_eff_where_sp_feed_force,					// 140	// ����� ���� : feed
	en_eff_where_sp_feed_concentration,			// 141	// ����� ���� : feed
	//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
	en_eff_where_sr_safe,						// 142	// ���� ���� ����ȿ��.
	en_eff_where_sr_light_crime,				// 143	// ����� ����ȿ��.
	en_eff_where_sr_heavy_crime,				// 144	// �߹��� ����ȿ��.
	en_eff_where_resist_all,					// 145	// ��� ���׷�.
	en_eff_where_resistance_battleroom_poison,	// 146	// �� ���� ���� : ó�� �߰��ÿ��� �� ������ �����ְ�, �� ���� �߰��ÿ� �� �Ӽ��� �˻��Ѵ�.
	//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
	en_eff_where_reduce_rb_time,				// 147	// ��Ȱ �޿��� �ð� ���� : 20080228 general/additive
	en_eff_where_reduce_rb_exp,					// 148	// ��Ȱ �޿��� ����ġ�϶�% ���� : 20080228 general/additive
	en_eff_where_protect_rate,					// 149	// ���� ���� Ȯ���� % ������ ���� : 20080228 general/additive
	en_eff_where_dam_inc_for_human_type=150,	// 150	// �ΰ��� ���Ϳ��� ���ݷ��� %������ ���� : 20080228 general/additive
	en_eff_where_dam_inc_for_animal_type,		// 151	// ������ ���Ϳ��� ���ݷ��� %������ ���� : 20080228 general/additive
	en_eff_where_dam_inc_at_day,				// 152	// �� �ð��� ���ݷ��� %������ ���� : 20080228 general/additive
	en_eff_where_exp_inc_at_day,				// 153	// �� �ð��� ����ġ�� %������ ���� : 20080228 general/additive
	en_eff_where_dam_inc_at_night,				// 154	// �� �ð��� ���ݷ��� %������ ���� : 20080228 general/additive
	en_eff_where_exp_inc_at_night,				// 155	// �� �ð��� ����ġ�� %������ ���� : 20080228 general/additive
	en_eff_where_abs_defend,					// 156	// ������ : 20070421 : general/additive
	en_eff_where_shield_out_wound,				// 157	// �ܻ�� : 20080421 : additive
	en_eff_where_existance,						// 158	// ���簨 : 20080421 : general/additive ( sub = ���簨 �϶� )
};


enum EN_CONDITION_ID
{
	en_con_id_none=0,						//	0	�ƹ��͵� ����. ���۰�.
	en_con_id_gijul,						//	1	����.
	en_con_id_sturn,						//	2	����(����)
	en_con_id_silent,						//	3	ħ��
	en_con_id_sleep,						//	4	����
	en_con_id_jumhyul,						// 	5	����
	en_con_id_wound_poison,					//	6	������
	en_con_id_nerve_poison,					// 	7	�Ű浶
	en_con_id_cri_poison,					//	8	ġ�絶
	en_con_id_honlan,						//	9	ȥ��
	en_con_id_hwangak,						//	10	ȯ��
	en_con_id_hwanchung,					// 	11	ȯû
	en_con_id_fear,							//	12	����
	en_con_id_lose_sight,					//	13	�Ǹ�
	en_con_id_mask,							// 	14	��
	en_con_id_light,						//	15	����
	en_con_id_transparent,					// 	16	����
	en_con_id_ice,							//	17	����
	en_con_id_wound,						// 	18	����
	en_con_id_joohwa,						//	19	��ȭ�Ը�
	en_con_id_drink,						//	20	����
	en_con_id_never_die,					//	21	�һ�
	en_con_id_indestructive,				//	22	�ұ�
	en_con_id_no_enemy,						// 	23	����
	en_con_id_illusion,						//	24	ȯ��
	
	en_con_id_bingwui,						//  25  ����
	en_con_id_stable,						//  26  ������
	en_con_id_youlhyoul,					//  27	����
	en_con_id_after_rebirth,				//  28	��Ȱ �޿���

	en_con_id_gm_trans,						//	29	��� ����.
	en_con_id_initial_no_enemy				//	30	��Ȱ, ���ڸ� ��Ȱ, ���� �̵� �ʱ⿡ 5�ʰ� ���� ����.

} ;

enum EN_RESISTANCE_POISON
{
	en_resistance_poison_0=0,
	en_resistance_poison_1,
	en_resistance_poison_2,
	en_resistance_poison_3,
	en_resistance_poison_count
} ;

// ����� ���� ����̴�. 
#define _GM_TRANS_CONDITION_ID_				(0x00000001<<28)			// �켱, �Ϲ� �������� �Ѵ�.


#define	_EFFECT_ID_MASK_			347		// ������ ���� ��¿�� ���� �ϵ� �ڵ�

#define	_BLOOD_ITEM_ID_				19		// ���� �������� ���̵�. �ϵ��ڵ��̴�.


/* =====================================================================================
	��ų �Ŵ���. - ���� Ʈ���� ���� Ʈ�� 
===================================================================================== */

struct _effect
{
	short		id;					// effect table(EffectScript.txt ����)�� ���ǵǾ� �ִ� id
	short		prob;				// ����ȿ���� �ߵ��� Ȯ��(skill table ����)
	short		value;				// ����ȿ���� �ߵ��� �� ����� ��(skill table ����)
	u_short		effect_prop;		// % ���� enum ��. ��ų ���̺����� ������ �����.
									//////////////////////////////////////////////////////////////////////////
									// Property
									// none = ����. �̰����� �����ϸ�, ó������ ����.
									// general 		= �Ϲ� �Ӽ�
									// initial_once		= ó���� �ѹ� ����ǰ� ������
									// apply_additive	= ������ ��󿡰� ����
									// bonus_additive	= ������ Additive : ���ݽÿ� ���ʽ��� ��.
									// reflection		= ���ø��� �ǵ���
									// only_once		= �� �ѹ� : �����ϸ� �����
									// feed			= ���ݽ� From ���� ��������

	char		value_type;			// %
									//////////////////////////////////////////////////////////////////////////
									// Value Type : ���밪 ��꿡 ���.
									// add_value		= ������ ����
									// add_percent		= %�� ����
									// sub_value		= ������ ����
									// sub_percent		= %�� ����

	u_char		where;				// % ��� ����...
	u_char		from;				// % ��𿡼� ���� ���ΰ�.
									//////////////////////////////////////////////////////////////////////////
									// From Target Type : 'feed', 'trans' �Ӽ������� ����, none �� ���� ����. 
									// none  �⺻ �Ӽ� : �̰� ���� �ȵ�. ��ũ��Ʈ ������ ó����.
									// hit,			�� ���������� 
									// life,		������ ü�¿���
									// force,		������ ���¿���
									// concentration	������ ��������

	u_char		condition_flag_id;	// %
									// EN_CONDITION_ID ����
	
	u_short		count;				// apply_additive�� ����� �ð�

	char		bInterval;			// ������ ���� ���ΰ�. 0/1 : ��κ� 0, �ߵ� � 1
									// true�� ���, 3�ʸ��� �����

	char		cPadding;			// �е�.


	_effect *	pNext;		// ���� ��ų�� NULL ��. 
							// �ϳ��� ��ų/������ ���� ���� effect�� �����Ǿ� ���� �� �����Ƿ�,
							// �� effect���� ����Ʈ�� �����ȴ�. ������ effect�� NULL ���� ���� ��.
	
	_effect()
	{
		::ZeroMemory( this, sizeof(_effect) ) ;
	}
} ;


//#################################################################################
//
//	struct 
//
//###################################################################### LHS Added....

/* =====================================================================================
	���� - Ư�� ������ ���� ȿ�� ���ʽ�
===================================================================================== */
struct _sEffectBonus_of_Mastery
{
	short	sEffect_Prob ;
	short	sEffect_Time_Value ;
	short	sEffect_Time_Percent ;
	short	sEffect_Value_Value ;
	short	sEffect_Value_Percent ;
	short	sReserved ;						// �׳� size�� 12 byte�� ���߷���.

	void Apply( _effect * p )
	{
		if( p )
		{
			if( sEffect_Prob )
				p->prob += sEffect_Prob;
			if( sEffect_Time_Percent )
				p->count += static_cast<u_short>( (int)p->count * sEffect_Time_Percent / 100 );
			if( sEffect_Time_Value )
				p->count = static_cast<u_short>((int)p->count + sEffect_Time_Value);
			if( sEffect_Value_Percent )
				p->value += static_cast<short>( (int)p->value * sEffect_Value_Percent  );
			if( sEffect_Value_Value )
				p->value += sEffect_Value_Value;
		}
	}
} ;


/* ============================================================================
	�����ڷκ��� ���� ��꿡 �ʿ��� ������ ���ͼ�, 
	�ǰ����ڿ��� �Ѱ��ش�. 
============================================================================ */
// �÷��̾� �⺻ ���� �Ķ����.
struct _playerAtkInfo
{
	// P : Physical : ����, 
	// M : Mental : ��� 
	int		iP_AtkDamage ;			// ���� ���ݷ�
	int		iP_AtkRate ;				// ���� ���� ����ġ
	int		iP_Balance ;				// ���� ����
	int		iP_Defence ;				// ���� ����
	int		iP_AvoidRate ;			// ���� ȸ����
	int		iP_DefenceRate ;			// ���� ���

	int		iP_Cri_AtkRate ;			// ġ��Ÿ ����ġ : ����
	int		iP_Cri_DefenceRate ;		// ġ��Ÿ ��� Ȯ��	: ����

	int		iM_Cri_AtkRate ;			// ġ��Ÿ ����ġ : ���.
	int		iM_Cri_DefenceRate ;		// ġ��Ÿ ��� Ȯ�� : ���.

	int		iM_AtkDamage ;			// ��� ���ݷ�
	int		iM_AtkRate ;				// ��� ���� ����ġ
	int		iM_Defence ;				// ��� ����
	int		iM_AvoidRate ;			// ��� ȸ����
	int		iM_DefenceRate ;			// ��� ���
	int		iM_Balance ;				// ��� ����
	int		iM_Cri_Damage ;			// ��� ġ��Ÿ ���ݷ�
} ;


// 4�� ������.
// ��.	0
// ��	1
// ȥ	2
// ��	3
// ���� �Ǵ� ȣ��Ÿ�Ͽ� ���� '�߰�'�Ǵ� ��
struct __curSkillAtkInfo
{
	int		iAttack_Max_Damage ;
	int		iAttack_Min_Damage ;
	int		iAttack_Rate ;			// �÷��̾� �⺻ ���� ������

	int		iAvoid_Rate ;				// ȸ����.

	int		iCritical_Bonus ;			// ġ��Ÿ ���ʽ�.
	
	short sNeedForce ;
	short sNeedMind ;

	short skillID ;
	char  skillLevel ;

	char cSkillAtkPoint ;			// ��ų�� Ÿ��.
	
	char  skillProperty ;			// ��1 ��2 ��3 ��4 �� �Ӽ�. 

	char	cSkillAngle ;			// 0, 1, 2 
	char	cApplyTargetType ;		// ������ ����� �Ǵ� �͵�.
	
	int		iCoolTime;

	float	fApplyRadius ;			// ���� �ݰ�.
	float	fAttackDistance;

	_sEffectBonus_of_Mastery *	pMasteryBonus ;
	
	_effect *	pGeneralList ;		//
	_effect *	pAdditveList ;		// 
} ;

struct __curSpellAtkInfo
{
	int		iAttack_Max_Damage ;
	int		iAttack_Min_Damage ;
	int		iAttack_Rate ;			// �÷��̾� �⺻ ���� ������
	int		iDefence ;

	int		iCritical_Bonus ;

	short sNeedForce ;	
	short sNeedMind ;
	
	int		spellTime;			//	���� ���ӽð�.
	short	spellID ;
	char	spellLevel ;
	
	char  spellActionType ;
	
	char  spellProperty ;			// ��1 ��2 ��3 ��4 �� �Ӽ�. 
	
	char cApplyAngle ;			// 0:one, 1:half angle, 2:all direction
	char cApplyTargetType ;		// ��ų�� Ÿ�� Ÿ��.

	int		iCoolTime;
	
	float fApplyRadius ;			// ���� �ݰ�.
	float	fAttackDistance;

	_sEffectBonus_of_Mastery *	pMasteryBonus ;
	
	_effect * pAdditiveList ;
} ;

struct _skilltotalAtkInfo
{
	int		iAttack_Max_Damage ;
	int		iAttack_Min_Damage ;
	int		iAttack_Rate ;			// �÷��̾� �⺻ ���� ������
	int		iDefence ;

	int		iAvoid_Rate ;				// ȸ����.
	int		iDefence_Rate ;			// ��� Ȯ��
	
	int		iCritical_AtkRate ;		// ũ��Ƽ�� Ȯ��
	int		iCritical_DefRate ;		// ũ��Ƽ�� ��� Ȯ��
	
	u_short sResult ;					// 0 : �Ϲ�, 1 : ũ��Ƽ��, 2 : �̽�..
	int		iBalance ;					// ��ü �߶���

	int		iCri_Dam_Bonus ;

	short sPropDamage[4] ;			// ����ȥ���� �߰� ������.
	short sPropResist[4] ;			// ����ȥ���� ���׷�

	_effect	BonusAdditive[_MAX_CHARAC_BONUS_ADDITIVE_] ;		// additive ���� ������ �����ؼ� ����. 
	_effect ItemEffect[_MAX_ITEM_BONUS_ADDITIVE_] ;				// 
} ;


struct _spelltotalAtkInfo
{
	int		iAttack_Max_Damage ;
	int		iAttack_Min_Damage ;
	int		iAttack_Rate ;			// �÷��̾� �⺻ ���� ������
	int		iDefence ;

	int		iAvoid_Rate ;				// ȸ����.
	int		iDefence_Rate ;			// ��� Ȯ��
	
	int		iCritical_AtkRate ;
	int		iCritical_DefRate ;		// ũ��Ƽ�� ��� Ȯ��
	
	u_short sResult ;					// 0 : �Ϲ�, 1 : ũ��Ƽ��, 2 : �̽�..
	int		iBalance ;					// ��ü �߶���

	int		iCri_Dam_Bonus ;

	short	skillID ;
	char	skillLevel ;

	short sPropDamage[4] ;			// ����ȥ���� �߰� ������.
	short sPropResist[4] ;			// ����ȥ���� ���׷�

	__curSpellAtkInfo curSpellAtk ;	// 
	
	//////////////////////////////////////////////////////////////////////////>>
	// 2006.04.04 molla, ���� ���� �ð��� ���� ���ݷ� ����Ǿ���
	int		iCasting;
	int		iDelay ;
	//////////////////////////////////////////////////////////////////////////<<
} ;

//////////////////////////////////////////////////////////////////////////
// ���ο� ����/��� ���� ����ü. 2005-04-04 Ray �߰�.
//////////////////////////////////////////////////////////////////////////

struct _bt_skill_atkinfo
{
	int		iAtk_Max_Dam ;
	int		iAtk_Min_Dam ;
	int		iAtk_Rate ;
	
	int		iCri_Atk_Rate ;

	short	sInnerLevel;

	u_short sResult ;

	u_char	cEffectCheck_Mask : 4 ;		// ������ ���� �ִ°�.
	u_char	cEffectCheck_res : 4 ;		// �Ⱦ���.
	u_char	cEffectCheck_res2 : 4 ;		// �Ⱦ���.
	u_char	cEffectCheck_res3 : 4 ;		// �Ⱦ���.
	
	int		iBalance ;
	
	short sPropDam[4] ;
	
	char skillProperty ;		// ����������õ 1,2,3,4,5,6 & 0(��)
	char cSkillAtkPoint ;		// Ÿ��.
	
	char cApplyAngle ;			// one, half angle, all direction
	char cApplyTargetType ;		// ��ų�� Ÿ�� Ÿ��.
	
	float	fRange ;
	float	fDistance;				// ���� ���� �Ÿ� : 0.0f �̸�, �׳� �Ѿ.
	
	int		iCriticalDam_Bonus ;	// ũ��Ƽ�� ������ ���ʽ� % ������.

	char	cPlayerMoonpa ;		// ���� ����
	char	cPlayerClass ;		// ���� ����

	short	sSkillID ;
	char	cAbs_AtkSuccess ;	// 0 - Normal, 1 - ��-���ݼ���
	char	cAbs_Critical ;		// 0 - Normal, 1 - ��-ġ��Ÿ.
	
	short	sMabi_Prob ;			// ���� �߻� Ȯ��. �⺻�� 0�̴�.
	short	sMabi_Time ;			// �⺻ 3�� �ܿ� �߰� �ð�.	

	short	sFeed_Life ;			// ���� ����.
	short	sFeed_Force ;			// ���� ����
	short	sFeed_Concentration ;	// ���� ����.

	short	sDamInc_For_HumanType;		// �ΰ��� ���Ϳ� ���� ���ݷ� ��� : �������� �߰��ȴ�.
	short	sDamInc_For_AnimalType;		// ������ ���Ϳ� ���� ���ݷ� ��� : �������� �߰��ȴ�. 

	short	sTime_Bonus;			// ��/�㿡 ���� ���ݷ� ���ʽ�.

	_sEffectBonus_of_Mastery	MasteryBonus[4] ;

	_effect * pApplyAdditive ;
	
	// �̺κ��� ũ�ϱ�, �ʿ��� ��, �ٽ� ������ bt_get_additive() �� ������.
	short		nCount ;
	_effect		skill_bonus[_MAX_CHARAC_BONUS_ADDITIVE_+_MAX_ITEM_BONUS_ADDITIVE_] ; 
} ;

struct _bt_spell_atkinfo
{
	int		iAtk_Max_Dam ;	// �ִ� ���� ��
	int		iAtk_Min_Dam ;	// �ּ� ���� ��

	int		iAtk_Rate ;		// ���� ����ġ(������ �ƴ�)
	int		iCri_Atk_Rate ;	// ũ��Ƽ�� ����ġ

	short	sInnerLevel;
	
	u_short sResult ;		// ���� ����/���� ��� �� ��� ����
	
	u_char	cEffectCheck_Mask : 4 ;		// ������ ���� �ִ°�.
	u_char	cEffectCheck_res : 4 ;		// �Ⱦ���.
	u_char	cEffectCheck_res2 : 4 ;		// �Ⱦ���.
	u_char	cEffectCheck_res3 : 4 ;		// �Ⱦ���.

	int		iBalance ;		// �ִ�/�ּ� ���ݷ� ������ ����ġ ��
	
	_effect * pApplyAdditive ;	// �⺻ ���� ȿ�� �̿ܿ� �߰��� Ȯ�������� ���Ǿ� ����Ǵ� ȿ��
								// (���纰�� �̸� ���Ǿ� ���̺�ȭ �� �� ���� �κ��̹Ƿ� ���� ����)

	short sPropDam[4] ;

	// ���� �Ӽ�
	int		iSpellTime;			// ������ �ð��� �̰����� �����Ѵ�.
	short sSpellID ;
	char cSpellStep ;
	char cSpellActionType ;
	char spellProperty ;		// ����������õ 1,2,3,4,5,6 & 0(��)
	char cApplyAngle ;			// one, half angle, all direction
	char cApplyTargetType ;		// ��ų�� Ÿ�� Ÿ��.
	float	fRange ;				// ������ ����Ǵ� ����
	float	fDistance;

	int		iCriticalDam_Bonus;	// ũ��Ƽ�� ������ ���ʽ� % ������.

	char	cPlayerMoonpa ;		// ���� ����
	char	cPlayerClass ;		// ���� ����

	char	cAbs_AtkSuccess ;	// 0 - Normal, 1 - ��-���ݼ���
	char	cAbs_Critical ;		// 0 - Normal, 1 - ��-ġ��Ÿ.

	short	sMabi_Prob ;		// ���� �߻� Ȯ��. �⺻�� 0�̴�.
	short	sMabi_Time ;		// �⺻ 3�� �ܿ� �߰� �ð�.	

	short	sFeed_Life ;			// ���� ����.
	short	sFeed_Force ;			// ���� ����
	short	sFeed_Concentration ;	// ���� ����.

	short	sDamInc_For_HumanType;		// �ΰ��� ���Ϳ� ���� ���ݷ� ��� : �������� �߰��ȴ�.
	short	sDamInc_For_AnimalType;		// ������ ���Ϳ� ���� ���ݷ� ��� : �������� �߰��ȴ�. 

	short	sTime_Bonus;			// ��/�㿡 ���� ���ݷ� ���ʽ�.

	_sEffectBonus_of_Mastery	MasteryBonus[4] ;
	

	short		nCount ;
	_effect		skill_bonus[_MAX_CHARAC_BONUS_ADDITIVE_+_MAX_ITEM_BONUS_ADDITIVE_] ; 
} ;

// 2005-04-04 Ray, ��� ����
struct _bt_definfo
{
	int		iBlock_Rate ;	
	int		iAvoid_Rate ;
	int		iCri_Def_Rate ;
	int		iDefence ;
	
	char	skillProperty ;			// ��1 ��2 ��3 ��4 �� �Ӽ�. 

	char	cNeverDie ;				// �һ� : ���� ������ ���� �ʴ´�.
	char	cIndestructive ;		// �ұ� : ��� ������ ���� �ʴ´�.
	char	cNoEnemy ;				// ���� : ��� ��� ������ ���� �ʴ´�.
	char	cIllusion ;				// ȯ�� : ��� ����� ȸ��!! �ݻ�� �Ͼ�� �ʴ´�.

	char	cAbs_Avoid ;			// �� ȸ�� 0 = normal, 1 = on
	char	cAbs_Reflect ;			// �� �ݻ� 
	
	char	cClass ;				// ����.

	u_char	cEffectCheck_Mask : 4 ;		// ������ ���� �ִ°�.
	u_char	cEffectCheck_res : 4 ;		// �Ⱦ���.
	u_char	cEffectCheck_res2 : 4 ;		// �Ⱦ���.
	u_char	cEffectCheck_res3 : 4 ;		// �Ⱦ���.
	
	short	sMabi_Defence ;			// ���� ��� Ȯ��.

	short	sResist[4] ;
} ;

struct _bt_action_info
{
	short	sSkillID ;
	char	cSkillStep ;
	char	cPad ;

	int		iAttack_Rate ;
	short	sLevel ;
} ;


// ������ �⺻ �Ķ����.
struct _sItemAttr 
{			
	// ���� ������ ���� 
	int					m_item_weapon_is_spell ;		// 0 - physical weapon, not zero - spell weapon
	int					m_item_weapon_p_min ;
	int					m_item_weapon_p_max ;

	int					m_item_weapon_m_min ;
	int					m_item_weapon_m_max ;
	
	// ������ �⺻ �ִ�/�ּ� ������.
	int					m_item_weapon_def_min ;			// �ؼ��� ��û����, 
	int					m_item_weapon_def_max ;			// ������ �⺻ ���������� ���� ������ ����(%)�� ��Ų��.
	
	// ���� ����ġ ����
	int					m_item_weapon_p_attack_rate ;	// ���ݼ���ġ ���� : �⺻ ��ġ�� ������� ���´�. 
	int					m_item_weapon_m_attack_rate ;	// ���ݼ���ġ ���� : �⺻ ��ġ�� ������� ���´�. 
	
	// ����
	int					m_item_weapon_balance ;
	
	// ġ��Ÿ ����
	int					m_item_weapon_p_critical ;		 
	int					m_item_weapon_m_critical ;		 
	
	// ������ ���� : �ǻ���.
	int					m_item_defence_max ;	// ����
	int					m_item_defence_min ;
	int					m_item_avoid_rate ;		// ȸ����
	int					m_item_attack_rate ;	// ���� ����ġ
} ;


//////////////////////////////////////////////////////////////////////////
////  2004/11/26 �߰�.
//////////////////////////////////////////////////////////////////////////
// B/H, Condition, Item ���� �߰� ȿ�� ���.


// �������� ���� Ư�� ������ �����!!! 2005/01/21 
// �������� ���, ���� ������ �� �δ� �ͺ��ٴ�, 
// ���� ���԰� ��ġ�Ǵ� Ư��ġ�� �ϳ��� �ΰ�, ����� �� �� ������ Ư��ġ�� �����Ų��.
// �׸��� �ʿ��� ��, ���� ����.

// �������� ���� -> �� ������ __item_result_entry�� ����Ͽ�, _item_effect_result ���·� �����Ŵ.
//  ==> calc ���� ������ ����. ( ������ ���� �� �Ʒ��� ������ ������� �̿���. )

//  ## �������� ���ʽ� Ÿ�� ##
enum
{
	en_bonus_type_none=0,			// none...
	en_bonus_type_battle,			// Battle Bonus
	en_bonus_type_max_power,		// Max Power Bonus
	en_bonus_type_point,			// Stat Point Bonus
	en_bonus_type_recover,			// Recover(Regen) Bonus
	en_bonus_type_etc				// Etc Bonus
} ;




// ## ���� �������� ���� ������ ����� : �����ۿ� ���� ���ʽ��� ������, ���õȴ�. ##
enum
{
	en_battle_bonus_atr_prob=0,
	en_battle_bonus_atr_value,
	
	en_battle_bonus_avr_prob,
	en_battle_bonus_avr_value,
	
	en_battle_bonus_dam_prob,
	en_battle_bonus_dam_value,	// 5
	
	en_battle_bonus_def_prob,
	en_battle_bonus_def_value,
	
	en_battle_bonus_blo_prob,
	en_battle_bonus_blo_value,
	
	en_battle_bonus_bal_prob,	// 10
	en_battle_bonus_bal_value,

	en_battle_bonus_cri_dam_bonus,

	en_battle_bonus_p_bal_prob,
	en_battle_bonus_p_bal_value,
	
	en_battle_bonus_m_atr_prob,	// 15
	en_battle_bonus_m_atr_value,
	
	en_battle_bonus_m_avr_prob,
	en_battle_bonus_m_avr_value,
	
	en_battle_bonus_m_dam_prob,
	en_battle_bonus_m_dam_value,	// 20
	
	en_battle_bonus_m_def_prob,
	en_battle_bonus_m_def_value,
	
	en_battle_bonus_m_blo_prob,
	en_battle_bonus_m_blo_value,

	en_battle_bonus_m_bal_prob,		// 25
	en_battle_bonus_m_bal_value,
	
	en_battle_bonus_cri_atr_prob,
	en_battle_bonus_cri_atr_value,
	
	en_battle_bonus_cri_dfr_prob,
	en_battle_bonus_cri_dfr_value,	// 30

	en_battle_bonus_p_cri_atr_prob,
	en_battle_bonus_p_cri_atr_value,
	
	en_battle_bonus_p_cri_dfr_prob,
	en_battle_bonus_p_cri_dfr_value,

	en_battle_bonus_m_cri_atr_prob,		// 35
	en_battle_bonus_m_cri_atr_value,
	
	en_battle_bonus_m_cri_dfr_prob,
	en_battle_bonus_m_cri_dfr_value,

	en_battle_bonus_abs_defend,			//

	en_battle_bonus_weapon_dam_prob,
	en_battle_bonus_weapon_dam_value,	// 40

	en_battle_bonus_weapon_max_dam_prob,
	en_battle_bonus_weapon_max_dam_value,
	
	en_battle_bonus_weapon_min_dam_prob,
	en_battle_bonus_weapon_min_dam_value,
	
	en_battle_bonus_prop_dam_minus,		// 45
	en_battle_bonus_prop_dam_plus,
	en_battle_bonus_prop_dam_hon,
	en_battle_bonus_prop_dam_sin,

	en_battle_bonus_prop_resist_all,
	
	en_battle_bonus_prop_resist_minus,	// 50
	en_battle_bonus_prop_resist_plus,
	en_battle_bonus_prop_resist_hon,
	en_battle_bonus_prop_resist_sin,

	en_battle_bonus_spell_add_dam_value1,	// 2005-04-12 �߰� Ray.
	en_battle_bonus_spell_add_dam_value2,	// 55
	en_battle_bonus_spell_add_dam_value3,	
	en_battle_bonus_spell_add_dam_value4,

	en_battle_bonus_sk_mabi_prob,
	en_battle_bonus_sk_mabi_time,

	en_battle_bonus_sp_mabi_prob,		// 60
	en_battle_bonus_sp_mabi_time,

	en_battle_bonus_resist_mabi,			// ���� ���� Ȯ��.

	en_battle_bonus_dam_inc_for_human_type,
	en_battle_bonus_dam_inc_for_animal_type,

	en_battle_bonus_dam_inc_at_day,		// 65
	en_battle_bonus_dam_inc_at_night,
	
	en_battle_bonus_count
} ;

// ## �ִ�ġ ���ʽ��� ���� �����. ##
enum
{
	en_maxpower_bonus_life_prob=0,
	en_maxpower_bonus_life_value,
	
	en_maxpower_bonus_force_prob,
	en_maxpower_bonus_force_value,
	
	en_maxpower_bonus_concentration_prob,
	en_maxpower_bonus_concentration_value,

	en_maxpower_bonus_count
} ;

// ## ����Ʈ ���ʽ��� ���� ����� : �����ۿ� ����Ʈ ���ʽ��� ������ ���õȴ�. ## // 
enum 
{
	en_point_bonus_strength=0,	
	en_point_bonus_zen,
	en_point_bonus_intelligent,
	en_point_bonus_constitution,
	en_point_bonus_dexterity,
	en_point_bonus_all_stat_value,
	en_point_bonus_all_stat_percent,

	en_point_bonus_move_speed,
	en_point_bonus_attack_speed,

	en_point_bonus_fury_percent,

	en_point_item_drop_percent,				// 

	en_point_expinc_at_day,
	en_point_expinc_at_night,

	en_point_item_drop_rate,
	en_point_protect_success,
	
	en_point_reduce_rebirth_time,			// 
	en_point_reduce_rebirth_expdec,			// 

	en_point_existance,

	en_point_bonus_count
} ;

// ## ȸ�� ���ʽ��� ���� ����� : �����ۿ� ȸ�� ���ʽ��� ���� �� ���õȴ�. ##
enum
{
	en_recover_bonus_life_prob=0,
	en_recover_bonus_life_value,
	en_recover_bonus_force_prob,
	en_recover_bonus_force_value,
	en_recover_bonus_concentration_prob,
	en_recover_bonus_concentration_value,
	en_recover_bonus_count
} ;

// ## ��Ÿ ���ʽ��� ���� ����� : �����ۿ� ��Ÿ ���ʽ��� ���� ��, ���õȴ�. ## 
enum
{
	en_etc_bonus_feed_hit_to_life=0,
	en_etc_bonus_feed_hit_to_force,	
	en_etc_bonus_feed_hit_to_concentration,

	en_etc_bonus_feed_sphit_to_life,
	en_etc_bonus_feed_sphit_to_force,	
	en_etc_bonus_feed_sphit_to_concentration,
	
	en_etc_bonus_reflect_p_dam,
	en_etc_bonus_reflect_m_dam,

	en_etc_bonus_reflect_minus_dam,
	en_etc_bonus_reflect_plus_dam,
	en_etc_bonus_reflect_hon_dam,
	en_etc_bonus_reflect_sin_dam,

	en_etc_bonus_feed_life,
	en_etc_bonus_feed_force,
	en_etc_bonus_feed_concentration,
	
	// 2006.04.11 molla, >>>>
	en_etc_bonus_pury, // ������ ġ��Ÿ�� �¾��� ��� ȹ���ϴ� ���ʽ� �г뷮
	//<<<<<<<<<<<<<<<<<<<<<<<<
	
	en_etc_bonus_count
} ;


// 1. ���� �Ķ����
struct _sBattle_Bonus
{
	// ���� 
	int		m_iP_Atr_Bonus_Prob ;				// ����ġ �ۼ�Ʈ ���ʽ�	
	int		m_iP_Atr_Bonus_Value ;				// ����ġ �� ���ʽ�
	
	int		m_iP_Avr_Bonus_Prob ;				// ȸ���� �ۼ�Ʈ ���ʽ�
	int		m_iP_Avr_Bonus_Value ;				// ȸ���� �� ���ʽ�
	
	int		m_iP_Dam_Bonus_Prob ;				// ���ݷ� �ۼ�Ʈ ���ʽ�
	int		m_iP_Dam_Bonus_Value ;				// ���ݷ� �� ���ʽ�
	
	int		m_iP_Def_Bonus_Prob ;				// ���� �ۼ�Ʈ ���ʽ�
	int		m_iP_Def_Bonus_Value ;				// ���� �� ���ʽ�
		
	int		m_iP_Blo_Bonus_Prob ;				// ��� �ۼ�Ʈ ���ʽ�
	int		m_iP_Blo_Bonus_Value ;				// ��� �� ���ʽ�

	int		m_iBal_Bonus_Prob ;
	int		m_iBal_Bonus_Value ;
		
	int		m_iP_Bal_Bonus_Prob ;				// ���� �ۼ�Ʈ ���ʽ�
	int		m_iP_Bal_Bonus_Value ;				// ���� �� ���ʽ�
	
	// ���
	int		m_iM_Atr_Bonus_Prob ;				// ����ġ �ۼ�Ʈ ���ʽ�
	int		m_iM_Atr_Bonus_Value ;				// ����ġ �� ���ʽ�
		
	int		m_iM_Avr_Bonus_Prob ;				// ȸ���� �ۼ�Ʈ ���ʽ�
	int		m_iM_Avr_Bonus_Value ;				// ȸ���� �� ���ʽ�
		
	int		m_iM_Dam_Bonus_Prob ;				// ���ݷ� �ۼ�Ʈ ���ʽ�
	int		m_iM_Dam_Bonus_Value ;				// ���ݷ� �� ���ʽ�
		
	int		m_iM_Def_Bonus_Prob ;				// ���� �ۼ�Ʈ ���ʽ�
	int		m_iM_Def_Bonus_Value ;				// ���� �� ���ʽ�
		
	int		m_iM_Blo_Bonus_Prob ;				// ��� �ۼ�Ʈ ���ʽ�
	int		m_iM_Blo_Bonus_Value ;				// ��� �� ���ʽ�
	
	int		m_iM_Bal_Bonus_Prob ;				// ��� �ۼ�Ʈ ���ʽ�
	int		m_iM_Bal_Bonus_Value ;				// ��� �� ���ʽ�

	int		m_iCri_Atr_Bonus_Prob ;			// ũ��Ƽ�� ������ �ۼ�Ʈ ���ʽ�
	int		m_iCri_Atr_Bonus_Value ;			// ũ��Ƽ�� ������ �� ���ʽ�

	int		m_iCri_Dfr_Bonus_Prob ;				// 
	int		m_iCri_Dfr_Bonus_Value ;			// 

	int		m_iCri_Dam_Bonus ;					// ũ��Ƽ�� �� ���ʽ� �߰�.

	int		m_iP_Cri_Atr_Bonus_Prob ;			// ũ��Ƽ�� ������ �ۼ�Ʈ ���ʽ�
	int		m_iP_Cri_Atr_Bonus_Value ;			// ũ��Ƽ�� ������ �� ���ʽ�

	int		m_iM_Cri_Atr_Bonus_Prob ;			// ũ��Ƽ�� ������ �ۼ�Ʈ ���ʽ�
	int		m_iM_Cri_Atr_Bonus_Value ;			// ũ��Ƽ�� ������ �� ���ʽ�

	int		m_iP_Cri_Dfr_Bonus_Prob ;				// 
	int		m_iP_Cri_Dfr_Bonus_Value ;			// 

	int		m_iM_Cri_Dfr_Bonus_Prob ;				// 
	int		m_iM_Cri_Dfr_Bonus_Value ;			// 
	
	int		m_iAbs_Defend;						// ���� ���.
	

	int		m_iWeapon_Dam_Prob ;				// ���� �������� ���� ����
	int		m_iWeapon_Dam_Value ;				// ���� �������� ���� ����
	
	int		m_iWeapon_Max_Dam_Prob ;
	int		m_iWeapon_Max_Dam_Value ;

	int		m_iWeapon_Min_Dam_Prob ;
	int		m_iWeapon_Min_Dam_Value ;

	short	m_sP_Mabi_Probability ;				// ���� ����
	short	m_sP_Mabi_Time ;					// 

	short	m_sM_Mabi_Probability ;				// ��� ����.
	short	m_sM_Mabi_Time ;					// 
	
	short	m_sResist_Mabi_Probability ;		// ���� ���� Ȯ�� : ���� �߻� �����, 
												// �̰����� �ٽ� Ȯ�� ������, �� �Ʒ���, ����/ �ƴϸ� ����.

	short	m_sDamInc_for_human_type;			// �ΰ��� ���Ϳ� ���� ���ݷ� % ���( �������� % )
	short	m_sDamInc_for_animal_type;			// ������ ���Ϳ� ���� ���ݷ� % ���( �������� % )
	
	short	m_sDamInc_at_day;					// 
	short	m_sDamInc_at_night;					// 
	

	
	short	m_sProp_Damage_Value[4] ;			// ����ȥ���� �߰� ������.

	short	m_sProp_Resist_Value[4] ;				// ����ȥ���� ���׷�

	short	m_sSpell_Add_Dam_Percent[4] ;		// ����ȥ�� ���翡(Ư�����翡) �߰� �������� �ش�.
} ;



struct _sBonus_PointAndPower
{
	// ����Ʈ �κ�
	short	m_sStrength	;		//	�ٷ� ���ʽ�
	short	m_sZen	;			//	���� ���ʽ�
	short	m_sInt	;			//	���� ���ʽ�
	short	m_sCon	;			//	�ǰ� ���ʽ�
	short	m_sDex	;			//	��ø ���ʽ�

	// �ƽ� �Ŀ���.
	short	m_sMax_Life_Prob	;			//	�ִ� ü�� �ۼ�Ʈ ���ʽ�
	short	m_sMax_Life_Value	;			//	�ִ� ü�� �� ���ʽ�
	short	m_sMax_Force_Prob	;			//	�ִ� ���� �ۼ�Ʈ ���ʽ�
	short	m_sMax_Force_Value	;			//	�ִ� ���� �� ���ʽ�
	short	m_sMax_Concentration_Prob	;	//	�ִ� ���� �ۼ�Ʈ ���ʽ�
	short	m_sMax_Concentration_Value	;	//	�ִ� ���� �� ���ʽ�
	
	// �̵� �ӵ��� ���� �ӵ�.
	short	m_sMove_Speed ;						// ���� % ���̴�.
	short	m_sAttack_Speed ;					// 
	
	short	m_sAttr_Percent_Bonus ;			// ������ % ���(+) �Ǵ� �϶�(-) : ���� ������ �Ѵ�.( get_last... �Լ��� )

	short	m_sFury_Percent_Bonus ;			// �г� ȹ�淮 n% ����.

	short	m_sExpInc_at_day;
	short	m_sExpInc_at_night;

	short	m_sItem_Drop_Rate_Bonus;		// ���� ��� Ȯ�� ����
	short	m_sProtect_Success_Bonus;
	short	m_sReduce_Rebirth_Time_Bonus;	// ��Ȱ �޿��� �ð� ����
	short	m_sReduce_Rebirth_ExpDec_Bonus;	// ��Ȱ �޿��� ����ġ �϶� ����.
	
	short	m_sAttackPoint_Increase;		// %��. �ִ� ���簨���� % ������ ���.
} ;

// 4. ĳ���� ȸ�� ���ʽ�.
struct _sBonus_Recover
{
	short	m_sLife_Prob	;			//	ü�� �ڵ�ȸ�� �ۼ�Ʈ ���ʽ�
	short	m_sLife_Value	;			//	ü�� �ڵ�ȸ�� �� ���ʽ�
	short	m_sForce_Prob	;			//	���� �ڵ�ȸ�� �ۼ�Ʈ ���ʽ�
	short	m_sForce_Value	;			//	���� �ڵ�ȸ�� �� ���ʽ�
	short	m_sConcentration_Prob	;	//	���� �ڵ�ȸ�� �ۼ�Ʈ ���ʽ�
	short	m_sConcentration_Value	;	//	���� �ڵ�ȸ�� �� ���ʽ�

	short	m_sResistance_poison_pk ;			// �п� ���� ����.
} ;



// 5. Feed, Reflection ����.
struct _sBonus_Etc
{
	// Feed : '���'���� '���'�� �� �ʿ�...
	short	m_sFeed_hit_to_life ;				// ���� �ִ� ������ Prob �� �ִ�. �����δ� �ȵ�.1000���� 1 ����
	short	m_sFeed_hit_to_force ;				// 1000���� 1 ����
	short	m_sFeed_hit_to_concentration ;		// 1000���� 1 ����

	short	m_sFeed_sphit_to_life ;				// 
	short	m_sFeed_sphit_to_force ;			//
	short	m_sFeed_sphit_to_concentration ;	//

	// Reflection
	short	m_sReflect_P_Dam ;					// ���� ������ �ݻ�( ��ų )
	short	m_sReflect_M_Dam ;					// ��� ������ �ݻ�( ���� )

	short	m_sReflect_4_Prob_Dam[4] ;				// ��/��/ȥ/�� ����
} ;

// 6. ����/���� ���� ��ȣ�� ���� ���� �Ķ����.
// ���� ��ȣ�� ���� ����, ���� ȿ�� ����Ʈ ��ȣ�� ã�Ƽ� ���쵵�� ����.
// ����Ʈ�� ���� ������ ���°� �߰� �Ǿ��� ��, ���� ��ȣ �ϳ������δ� ó���� �Ұ�����.
// ����, ���� ���� �� ������, (1) �켱 �ܵ�� ����Ʈ���� '331' ���� ã�� ���ְ�, 
// ���ٸ�, (2) extra ȿ������ ã�Ƽ� ���ش�.
struct _sBonus_Shield
{
	short	m_sForce_Defence ;			// ���� ��� �ܵ�� ���� ��ȣ. ( �⺻�� 0 )
	short	m_sConcentration_Defence ;	// ���� ��� �ܵ�� ���� ��ȣ. ( �⺻�� 0 ) 
	
	short	m_sAbs_Avoid ;				// �� ȸ�� �ܵ�� ����. ( �⺻�� 0 )
	short	m_sAbs_Reflect ;			// �� �ݻ� �ܵ�� ����. ( �⺻�� 0 )
	short	m_sAbs_All_Atk ;			// �� ���� ���� �ܵ�� ����. ( �⺻�� 0 )
	short	m_sAbs_All_Atk_Cri ;		// �� ġ�� ���� �ܵ�� ����. ( �⺻�� 0 )
	
	short	m_sShield_Skill_Atk ;		// ���� - ����. 
	short	m_sShield_Spell_Atk ;		// ���� - ���.
	short	m_sShield_All_Atk ;			// ���� - ���.

	short	m_sShield_OWound;

	short	m_sShield_4_Prop[4]; 		// ���� - ����ȥ��.
} ;


/* ============================================================================
	��ġ ���� ������ 
============================================================================ */
struct _posInfo
{
	float x ;
	float z ;
	short nTile ;
} ;


/* ============================================================================
	���Ͱ� ������ �������� ����
============================================================================ */
struct __ITEM_SPAWN_INFO {
	char		cDropLevel;		//������ �й�� �ش� ������ ������ �ٰ��Ͽ� �߻��ϴ� �����ۿ� ������ �� ������ ����
	char		citem_type ;
	u_char		ucitem_merge ;
	u_char			ucslot_index;	//
	short		sitem_rank ;
	int			iitem_rate;

	__ITEM_SPAWN_INFO *		pNext;
} ;



/* ============================================================================
	���� ����
============================================================================ */

struct _point 
{
	float m_posX ;
	float m_posZ ;
	
	_point() : m_posX(0.0f), m_posZ(0.0f) {}
	_point( float x, float z ) : m_posX(x), m_posZ(z) {}
	inline void operator=( const _point& p ) { m_posX = p.m_posX ; m_posZ = p.m_posZ ; }
} ;

struct _point3D{
	float m_posX ;
	float m_posY ;
	float m_posZ ;
	_point3D()	{ m_posX = 0.0f ; m_posZ = 0.0f ; m_posY = 0.0f ; }
} ;


struct _sRegion {
	_point	pt1 ;
	_point	pt2 ;
	_point	pt3 ;
	_point	pt4 ;
	
	_point	ptCenter ;
	_point	ptUnit1 ;		// p3 - p4
	_point	ptUnit2 ;		// p1 - p4
	
	char	mode ;			// 0 for axis arrange, 1 for random...
	u_char	isEmpty : 1 ;
	u_char	ucProperty : 7 ;	// ���� �Ӽ� : 0 - �Ϲ�, 1 - �浵 ����, 2 - �鵵 ����, 3 - �պ񿡼� ������ ����.
	short	index ;				// ���� �ε���
	short	category;			// 
	short	reserve;			//

	_sRegion(){
		ZeroMemory( this, sizeof(_sRegion) );
		isEmpty = 1 ;
	}
};


struct __warpIndex_for_multi
{
	short	toZoneIndex ;
	short	toRegionIndex ;

	__warpIndex_for_multi * pNext ;		// ��Ƽ ������ �ִٸ�, ���ø�ũ�� ����Ʈ��.
} ;

struct _sWarpRegion{
	short myGateIndex ;
	short toZoneIndex ;
	short toRegionIndex ;
	
	_sRegion  region ;

	int		m_nMultiCount ;		

	__warpIndex_for_multi * pFirst ;	// 

	_sWarpRegion()
	{
		m_nMultiCount = 0 ;
		pFirst = NULL ;
	}

	~_sWarpRegion()
	{
		__warpIndex_for_multi * p = pFirst ;
		__warpIndex_for_multi * pdel = p ;
		if( p )
		{
			p = p->pNext ;
			delete pdel ;
			pdel = p ;
		}
	}

	short has_multi_to_zone( short zone )
	{
		short region = 0 ;
		__warpIndex_for_multi * p = pFirst ;
		while( p )
		{
			if( p->toZoneIndex == zone ) 
			{
				region = p->toRegionIndex ;
				break ;
			}

			p = p->pNext ;
		}

		return region ;
	}

	void add_multi( short zone, short region ) 
	{
		__warpIndex_for_multi * pMulti = new __warpIndex_for_multi ;

		assert( pMulti ) ;

		pMulti->toZoneIndex = zone ;
		pMulti->toRegionIndex = region ;

		pMulti->pNext = pFirst ;

		pFirst = pMulti ;

		++m_nMultiCount ;
	}


	void show_multi()
	{
		printf( "=== Gate %d : Multi gate count - %d\n", myGateIndex ) ;
		__warpIndex_for_multi * p = pFirst ;
		if( p )
		{
			printf( " - To %2d, Region %3d\n", p->toZoneIndex, p->toRegionIndex ) ;
			p = p->pNext ;
		}
		
	}
};

struct _sPvpRegion {
	short	index ;
	short	type ;
	short	safeRegion[4] ;
	short   completeRegion ;

	_sRegion	region ;
} ;


struct _UNIT_VECTOR
{
	float ux ;
	float uz ;
} ;


typedef struct tagDNpcItemSpawnInfo 
{
	char		cReturnGoldLevel;	//�� �й�� �ش� ������ ������ �ٰ��Ͽ� �߻��ϴ� ���� ������ �� ������ ����
	u_int		uiReturn_gold_min ;
	u_int		uiReturn_gold_max ;
	int			iReturn_gold_prob ;
	
	__ITEM_SPAWN_INFO *	pFirst;

	void add_return_item( __ITEM_SPAWN_INFO * pitem )
	{
		if( !pFirst )
		{
			pFirst = pitem;
			pitem->pNext = NULL;
		}
		else
		{
			__ITEM_SPAWN_INFO * p = pFirst;
			while( p->pNext )
			{
				p = p->pNext;
			}
			p->pNext = pitem;
			pitem->pNext = NULL;
		}
	}

} _DNpcItemSpawnInfo, *PDNPCITEMSPAWNINFO ;


// ������ ���� ������ ��Ÿ����, 
// �����ÿ� �μ��μ� ���δ�.
struct _sDNpcAttackInfo
{
	int		iAttack_Rate ;
	int		iAttack_Damage ;
	
	int		iCritical_Rate ;

	int		iReflectDamage ;		// �ݻ�Ǵ� ������.

	short	sAddEffect_ID ;			// Ư�� ���ݿ� ���� �߰��Ǵ� ȿ�� 1
	short	sAddEffect_Level ;		// Ư�� ���ݿ� ���� ���밪.
	
	short	sMonsterLevel ;			// ������ ����.( �� �������� �ʿ��ϴ� )


	char	cM_Attribute ;			// �������� �Ӽ�.
	bool	bPatrolDNPC;			// ����ΰ�.
	u_char	ucAttackClass;			// 0 - �������� ���� ��, 1 - ������ ���� ��.
#ifdef _PD_CASTLE_STEP_1_
	u_char	ucAssignPos;			// EN_MOB_ASSIGN_POSITION
#endif

	int		iPropDamage;			// �Ӽ� ���� ������
	u_char	ucPropWhere;				// �Ӽ� : 0(��) 1(��) 2(ȥ) 3(��)
} ;


enum{
	_FLAG_PARTY_FIELD_ = 0 ,
	_FLAG_PARTY_BOSSROOM,
} ;
struct _sPartyExp
{
	int			exp ;	
	short		sMobLevel ;
	char		cReserve ;
	char		cMode ;				// 0.�Ϲ� �ʵ�  1.������
	
	explicit _sPartyExp() : exp(0), sMobLevel(0), cReserve(0), cMode(0) {}
	explicit _sPartyExp( int v1, short v2, char v3, char f) : exp(v1), sMobLevel(v2), cReserve(v3), cMode(f) {}

	inline void add_exp( int v )	{ exp += v ; }	
	inline void set_mode( char f )  { cMode = f ; }
} ;



// �α׼��� ���� ����..
#define LOG_SVR_INFO_FILE					"nfofile\\LogServer.Inf"

#define WORD_LIMIT							(65535)

//////////////////////////////////////////////////////////////////////////
// ���� �Ӽ��� ��� ���(�����ڼӼ�vs�����ڼӼ�)�μ� ����� ���ʽ� ȿ��. ( ����� ���� ���̴�. )
enum 
{	
	en_prop_bonus_none=0,
	en_prop_bonus_ilgyuk_mabi,
	en_prop_bonus_bigyuk_mabi,
	en_prop_bonus_pilsal_mabi,
	en_prop_bonus_chosik_mabi,
	en_prop_bonus_resist_mabi
} ;

struct _sPropBonus
{
	char  cAttackerProp ;
	char  cDefenderProp ;
	u_short usResult ;

	short sBonusIndex ;			// ���ʽ� �ε��� : ���� prop_bonus ��.
	short sBonusProbabilty ;	// ���ʽ� Ȯ��.


	_sPropBonus() : cAttackerProp(0), cDefenderProp(0)
	{}

	_sPropBonus( char attacker, char defender ) 
		: cAttackerProp( attacker ), cDefenderProp( defender )
	{}
} ;


//////////////////////////////////////////////////////////////////////////
// ���� ���ݿ� ���̴� �⺻ ����Ÿ ��
// �������� ���� ���� ������ �ٸ� ��ü���� �����Ͽ�, ���� �����.
struct _sRegion_Attack_Base
{
	short		sAttackRate ;	// �������� ���� ������.
	short		sDamage ;		// �⺻ ������.
	short		sProperty ;		// ������������õ.
	
	u_short		usFireCharac ;	// ������ ����ũ ���̵�.

	_point		ptCenter ;		//	���� ������ �߽� 
	_point		vDirection ;	//
	short		sAngle ;		// ���� ����( all, half.. ), ��ų ���̺����� angle ���� ���� ����. 
	
	short		sAddDamage[4] ;	// �߰� ������. 0�̸� ���� �Ӽ� ����. 
} ;




/* //////////////////////////////////////////////////////////////////////////
	GS �� Ŭ���̾�Ʈ���� �ð��� ǥ���ϱ� ���� ����ϴ� ����ü��. 
	�����, ������ �ٲ��� �� ��.!!!
*/ //////////////////////////////////////////////////////////////////////////
struct _sND_TimeUnit	// client �� ������ ��Ŷ�� ���Ե�. �ٲ�� �Ǹ� Ŭ���̾�Ʈ���� �˷���� �Ѵ�. 
{
	// ��/��/��/��/��/��. End Time
	u_int			uiYear		: 5 ;	// Base 2000. Year = 2000 + uiYear( 0-31 )
	u_int			uiMonth		: 4 ;	// Value 1 - 12 ( 0 - 15 )
	u_int			uiDay		: 6 ;	// Value 1 - 31 ( 0 - 63 )
	u_int			uiHour		: 5 ;	// Value 0 - 23 ( 0 - 31 ) 
	u_int			uiMinute	: 6 ;	// Value 0 - 59 ( 0 - 63 )
	u_int			uiSecond	: 6 ;	// Value 0 - 59 ( 0 - 63 )

	_sND_TimeUnit()
		: uiYear(0), uiMonth(0), uiDay(0), uiHour(0), uiMinute(0), uiSecond(0)
	{
	}

	_sND_TimeUnit( const _sND_TimeUnit& rh)
		: uiYear(rh.uiYear), uiMonth(rh.uiMonth), uiDay(rh.uiDay), 
		uiHour(rh.uiHour), uiMinute(rh.uiMinute), uiSecond(rh.uiSecond)
	{
	}

	void Print()
	{
		printf( "%u-%u-%u %u:%u:%u", uiYear+2000, uiMonth, uiDay, uiHour, uiMinute, uiSecond );
	}

	time_t GetValueType()
	{
		time_t value = uiSecond | (uiMinute<<6) | (uiHour<<12) | (uiDay<<17) | (uiMonth<<22) | (uiYear<<25) ;
		return value;
	}

	void SetTime( unsigned int y, unsigned int mon, unsigned int d, unsigned int h, unsigned int m, unsigned int s = 0 )
	{
		uiYear = y-2000;
		uiMonth = mon;
		uiDay = d;
		uiHour = h;
		uiMinute = m;
		uiSecond = s;
	}
	
	/*
	DWORD GetValueType_TimeUnit()
	{
		DWORD value = uiYear;
		if( value )
		{
			value = uiYear*_PITEM_MULTI_YEAR_ +			\
				uiMonth*_PITEM_MULTI_MONTH_ +			\
				uiDay*_PITEM_MULTI_DAY_ +				\
				uiHour*_PITEM_MULTI_HOUR_ +				\
				uiMinute;
		}
		return value;
	}
	*/

	void SubMinute( u_int minute )
	{
		// �ѽð��̻����� ���� �� ������, 
		// 0,1�ÿ� ���� �� ���ٰ� �����Ѵ�.
		if( uiHour == 0 || uiHour == 1 || minute >= 120 )
			return;

		if( uiMinute >= minute )
			uiMinute -= minute;
		else 
		{
			uiHour -= 1;
			uiMinute = uiMinute + 60;

			if( uiMinute >= minute )
				uiMinute -= minute;
			else
			{
				uiHour -= 1;
				uiMinute = uiMinute + 60 - minute;
			}
		}
	}
};
#ifdef  _ORG_PROMOTION_
///< ���� �ð��� Dword ������ ����.
inline DWORD GetDWCurrentTime()
{
	DWORD tempTime(0);
	time_t cur;
	time(&cur);
	tm * curDay = localtime( &cur );
	_sND_TimeUnit curTimeUnit;
	curTimeUnit.uiYear		= curDay->tm_year-100;
	curTimeUnit.uiMonth		= curDay->tm_mon+1;
	curTimeUnit.uiDay		= curDay->tm_mday;
	curTimeUnit.uiHour		= curDay->tm_hour;
	curTimeUnit.uiMinute	= curDay->tm_min;
	curTimeUnit.uiSecond	= 0;
	tempTime = curTimeUnit.GetValueType();\
	return tempTime;
}
#endif//_ORG_PROMOTION_


/*#################################################################################
//
//  �̺�Ʈ ���̵� ���� : �⵵�� �������� ��������.
//
//###############################################################################*/
// �̺�Ʈ ��� �� ���� �ڵ�.
enum EN_EVENT_ERRNO
{
	en_event_success = 0,
	en_event_errno_blank,			// ���Դϴ�. ������...
	en_event_errno_no_key,			// �߸��� Ű��.
	en_event_errno_wait,			// ����Ŀ� �ٽ� �õ����ּ���.
	en_event_errno_event_end,		// �̹� ���� �̺�Ʈ �Դϴ�.
	en_event_errno_no_event,		// �������� �̺�Ʈ�� �ƴմϴ�.
	en_event_errno_used,			// �̹� ����� Ű�� �Դϴ�.
	en_event_errno_no_item_slot,	// �κ��丮�� ��á��
	en_event_errno_no_charac,		// ������ �ð��.	: �α׸� ���ؼ��� ���δ�.
	en_event_errno_fail_insert_item,// ������ ����������, �������� ���� ���ߴ�.
	en_event_errno_not_premium,		// �����̾� ������ �ƴϴ�.
	

	en_event_is_going_on=50,		// �̺�Ʈ�� �������̴�.( �ű����� �̺�Ʈ�� ����, ������ �� �̸� �˷��ش�. )
	
	en_event_errno_system=100,		// ��Ŷ ���̵��� �ý��� ����.
	en_event_errno_over_insert,		// ���� �Է�Ƚ�� �ʰ�
} ;

#define _EVENT_KEY_MAX_LENGTH_			(32)	// 31���ڱ����� ���.

#define MAKE_DNPC_EXIST_MESSAGE_FREQ	(3)	//DNPC�� ���� �޽����� SHOWME_INTERVAL(5.5��)�ð��� �� ���� ������. �׷��� �׷� ��� ���� ���ο� DNPC�� �޽������� �������� ���ϸ� �ʷ��� �� �ֱ� ������, �� ���� �� ������ ������ ������ �󵵼��� �����ϴ� ���� ���ڴ�. ���� �� ����� �󵵼��� �����Ѵ�. (EX: 3 -> �� ���� �� ���� �޽����� ����)
#define MAKE_USER_MOVE_MESSAGE_FREQ		(8)	//������ ������ ������ �ֺ��� ���Ϳ��� �ڽ��� ���� �޼����� ������ �ȴ�. ������ �׷� ���, �޼����� ���� ���ο� �ʹ� ���� �����Ǳ� ������, �� ���� �� ������ ������ ������ �󵵼��� �����ϴ� ���� ���ڴ�. ���� �� ����� �󵵼��� �����Ѵ�. (EX: 3 -> �� ���� �� ���� �޽����� ����)



enum LENGTH_CONSTANT
{
	en_max_lil		= 24,		// �ؽ� ���� ���� ��, �� ȸ�纰�� ������ ���̸� �����Ѵ�. ��, �ִ밪�� en_lil_nexon �̴�. 
	en_max_lpl		= 24,		// ��� ����
	en_charac_name_length = 12,
	en_or_name_length = 24, 
};


#ifdef _VINA_SHUTDOWN_20061023_
	#define VINA_SHUTDOWN__EXP_RATIO			0 //����ġ �����
	#define VINA_SHUTDOWN__QUEST_REWARD_RATIO	1 //��ȣǳ��� �����
#endif



#ifdef _PD_CASTLE_STEP_1_
enum EN_CASTLE_RELATION		// �����ܰ� ������� ���踦 ��Ÿ���� 
{
	en_castle_relation_none=0,			// ����� ���� ����.
	en_castle_relation_challenger,		// ���� �� ������
	en_castle_relation_owner,			// ����� ������.
	en_castle_relation_friend,			// ����� ��ȸ�� ���, �ٸ� ������
};
#endif


#define en_4_resist_min		(-100)		// ����ȥ�� �ּ� ���׷�
#define en_4_resist_max		(80)		// ����ȥ�� �ִ� ���׷�

#define TrimResistMinMax(resist)		max( min(en_4_resist_max,resist), en_4_resist_min)


#define DEAD_PENALTY_LEVEL		(24)	// 24 ���� ���ϴ� ����ġ/������ �г�Ƽ ����.


#define DELAYTIME_FOR_POTION	(2900U)		// ��� ���Ƿ��� 3���� ��� �����̸� ���´�.



//////////////////////////////////////////////////////////////////////////
// ���� ���� ������
//////////////////////////////////////////////////////////////////////////

#define MAX_MATERIAL_COLLECTIONITEM		(15)
#define MAX_MATERIAL_RESOURCEITEM		(16)
#define MAX_NEED_COLLECTION_SET			(5)
#define MAX_RETURNSET_COLLECTIONITEM	(4)
#define MAX_RETURN_COLLECTIONITEM		(3)


#ifdef _PD_PKEVENT_BOX_SPAWN_
//////////////////////////////////////////////////////////////////////////
// ������ : �пյ� ���� ���� �ð� ����
//////////////////////////////////////////////////////////////////////////
#ifdef _COMPANY_VINA_			// 2006/07/31 ���ʹ� ���� ��������
#define PAWANGDONG_ENTER_TIME			(10800)		// 3�ð�.
#else
#define PAWANGDONG_ENTER_TIME			(3600)		// 3�ð�.
#endif

#endif	// _PD_PKEVENT_BOX_SPAWN_

#endif // _THIS_IS_ND_GAME_SERVER_HEADER_GLOBAL_CONSTATNTS__3243SDLFHL34__

