#ifndef _NPC_BASE_CLASS_HEADER_DLJS98709SDJLJ87987LKSJ9LKJ7__
#define _NPC_BASE_CLASS_HEADER_DLJS98709SDJLJ87987LKSJ9LKJ7__

class CNPC_Base
{
protected:
	u_short		m_usID ;
	//float		m_fX ;
	//float		m_fZ ;

public:
	virtual ~CNPC_Base() =0 {} ;

	void set_id( u_short id ) { m_usID = id ; }
	u_short get_id() { return m_usID ; }

	//void set_pos( float x, float z ) { m_fX = x ; m_fZ = z ; }
	//float get_posx() { return m_fX ; }
	//float get_posz() { return m_fZ ; }
} ;


#endif