#include "..\stdafx.h"
#include "NDUtilClass.h"
#include "../NDExceptionReport/NDExceptionReport.h"
extern NDExceptionReport* g_pNDExceptionReport;


/*#################################################################################
//
//  CThread
//
//###############################################################################*/
CNDThread::CNDThread()
:  m_hThread(INVALID_HANDLE_VALUE)
{

}
      
CNDThread::~CNDThread()
{
   if (m_hThread != INVALID_HANDLE_VALUE)
   {
      ::CloseHandle(m_hThread);
   }

   m_hThread = INVALID_HANDLE_VALUE ;
}

HANDLE CNDThread::GetHandle() const
{
   return m_hThread;
}

bool CNDThread::Start()
{
   if (m_hThread == INVALID_HANDLE_VALUE)
   {
      unsigned int threadID = 0;

      m_hThread = (HANDLE)::_beginthreadex(0, 0, ThreadFunction, (void*)this, 0, &threadID);

      if (m_hThread == INVALID_HANDLE_VALUE)
      {
         return false ;
      }

	  return true ;
   }
   else
   {
      return false ;
   }
}

bool CNDThread::Wait() const
{
   if (!Wait(INFINITE))
   {
		return false ;
   }
   return true ;
}

bool CNDThread::Wait(DWORD timeoutMillis) const
{
   // TODO base class? Waitable?
   bool ok;

   DWORD result = ::WaitForSingleObject(m_hThread, timeoutMillis);

   if (result == WAIT_TIMEOUT)
   {
      ok = false;
   }
   else if (result == WAIT_OBJECT_0)
   {
      ok = true;
   }
   else
   {
      ok = false ;
   }
    
   return ok;
}

unsigned int __stdcall CNDThread::ThreadFunction(void *pV)
{
   int result = 0;

   CNDThread* pThis = (CNDThread*)pV;
   
   if (pThis)
   {
      TRY
      {
         result = pThis->Run();
      }
      CATCH_ALL
      {
		  printf( NULL, "CThread::Run Exception\n", "Exceptoin", MB_OK ) ;
      }
   }

   _endthreadex(result);

   return result;
}

void CNDThread::Terminate(
   DWORD exitCode /* = 0 */)
{
   if (!::TerminateThread(m_hThread, exitCode))
   {
      // TODO we could throw an exception here...
   }
}
