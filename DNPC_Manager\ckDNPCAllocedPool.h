//////////////////////////////////////////////////////////////////////////////////////////
//	ckDNPCAllocedPool

//	<EMAIL>
//	2004.10.14

#ifndef _CK_DNPCALLOCEDPOOL_H_
#define _CK_DNPCALLOCEDPOOL_H_

#include "ckDNPCSpecies.h"
// enable to use STL in MFC

class ckDNPCAllocedPool
{
private:
	//		DNPC Type,	Interface pointer list
	map<unsigned short, list<ckDNPCInterface* > * >	m_AllocedPool;
	CRITICAL_SECTION			cs;

	inline	void	Lock()		{	EnterCriticalSection(&cs);	}
	inline	void	UnLock()	{	LeaveCriticalSection(&cs);	}

	inline	ckDNPCInterface*	AllocNew(unsigned short type);

public:
	void	Create()	{	if( FALSE == InitializeCriticalSectionAndSpinCount(&cs, (0x80000000)|2000 ) ) throw "" ;	}
	void	Release();
	
	ckDNPCInterface*	GetAllocedPointer(unsigned short script_type);
	void				ReleaseAllocedPointer(ckDNPCInterface* pointer);
};

#endif