#include "../stdafx.h"

#ifdef _PD_GAMEGUARD_VER_1_

#include "../global/gGlobal.h"
#include "./ggsrv25.h"

GGAUTHS_API void NpLog(int mode, char* msg) {
	if(mode & (NPLOG_DEBUG | NPLOG_ERROR)) {
		Logout("[GGAUTH]%s", msg);
		//::PrintConsole("[GGAUTH]%s \n", msg);
	}
}

GGAUTHS_API void GGAuthUpdateCallback(PGG_UPREPORT report) {
	Logout("[GGAUTH]version update [%s]:[%ld]->[%ld]", 
			report->nType==1?"GameGuard Ver":"Protocol Num", 
			report->dwBefore,
			report->dwNext); 
	::PrintConsole("[GGAUTH]version update [%s]:[%ld]->[%ld] \n", 
			report->nType==1?"GameGuard Ver":"Protocol Num", 
			report->dwBefore,
			report->dwNext); 
};

#endif