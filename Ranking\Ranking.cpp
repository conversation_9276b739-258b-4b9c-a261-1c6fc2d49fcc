#include "..\stdafx.h"
#include "Ranking.h"



void CRankInfo::GetRankInfo( _Rank * pRank, size_t size )
{
	lock() ;
	try{
		
		for( u_int i = 0 ;  i < _MAX_RANK ; ++i )
		{
			if( size <= i )
			{
				break ;
			}

			pRank[i] = m_vRankInfo[i] ;
		}
	}
	catch (...) {
		::PrintConsole("[EXCEPTION] %s, %d \n", __FILE__, __LINE__ ) ;
		g_pErrorMsg->ErrMsg("[EXCEPTION] %s, %d \n", __FILE__, __LINE__ ) ;
	}		
	unlock() ;
}

int CRankInfo::GetMyRank(const char * name )
{
	
	int iMyRanking = -1 ;
	lock() ;
	try{
		
		for( u_int i = 0 ; i < _MAX_RANK ; ++i )
		{			
			if( m_vRankInfo[i].cCharacName[0] == name[0] && strcmp( m_vRankInfo[i].cCharacName, name ) == 0 )
			{
				iMyRanking = m_vRankInfo[i].sRanking ;
				break ;
			}
			/*
			if( i > _MAX_RANK )
				break ;
				*/
		}
	}
	catch (...) {
		::PrintConsole("[EXCEPTION] %s, %d \n", __FILE__, __LINE__ ) ;
		g_pErrorMsg->ErrMsg("[EXCEPTION] %s, %d \n", __FILE__, __LINE__ ) ;
	}		
	unlock() ;

	return iMyRanking ;

}

int CRankingManager::GetMyRanking( const char * name, const short rankType )
{
	if( name == NULL || rankType >= _ranking::_RANK_TYPE_MAX || rankType < 0 )
		return -1 ;

	int iMyRanking = -1 ;

	CRankInfo * pRankInfo = GetRankInfoPtr( rankType ) ;
	if( pRankInfo )
	{
		iMyRanking = pRankInfo->GetMyRank( name ) ;
	}

	return iMyRanking ;
}


u_short _GetRankingNickID( const short rankType, const short sMyRankingIndex )
{
	u_short usMyRankingNickID = 0 ;
	
	
	switch( rankType ) {
	case _ranking::_RANK_TYPE_VIMU :		
		break;
	case _ranking::_RANK_TYPE_LEVEL :
		{
			switch( sMyRankingIndex + 1 ) 
			{
			case 1:
				usMyRankingNickID = 311 ;
				break;
			case 2:
				usMyRankingNickID = 312 ;
				break;
			case 3:
				usMyRankingNickID = 313 ;
				break;
			case 4:
				usMyRankingNickID = 314 ;
				break;
			case 5:
				usMyRankingNickID = 315 ;
				break;
			case 6:
				usMyRankingNickID = 316 ;
				break;
			case 7:
				usMyRankingNickID = 317 ;				
				break;
			case 8:
				usMyRankingNickID = 318 ;				
				break;
			case 9:
				usMyRankingNickID = 319 ;				
				break;
			default:
				break;
			}
		}
		break;
	case _ranking::_RANK_TYPE_MARTIAL :
		break; 
	case _ranking::_RANK_TYPE_PART :
		break; 
	default:
		break;
	}
	
	
	return usMyRankingNickID ;
}