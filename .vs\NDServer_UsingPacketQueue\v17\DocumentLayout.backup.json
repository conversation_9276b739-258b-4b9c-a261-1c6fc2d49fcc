{"Version": 1, "WorkspaceRootPath": "D:\\Project\\NDServer\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Environment\\Environment_Read.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Environment\\Environment_Read.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Level_Luck\\LevelManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Level_Luck\\LevelManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Skill\\SkillManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Skill\\SkillManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\User\\ExceptionBattle.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:User\\ExceptionBattle.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\User\\User.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:User\\User.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\User\\User_Item_Part.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:User\\User_Item_Part.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\NineDragonsServer.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:NineDragonsServer.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\NPC\\SNpcManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:NPC\\SNpcManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Global\\Global_Function.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Global\\Global_Function.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Packets\\packet_for_ex_battle_room.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Packets\\packet_for_ex_battle_room.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\User\\ExceptionBattle.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:User\\ExceptionBattle.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Global\\Global_Define.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Global\\Global_Define.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Environment\\Environment_Read.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Environment\\Environment_Read.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Global\\FileInput.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Global\\FileInput.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\GameGuard\\ggsrv25.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:GameGuard\\ggsrv25.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\GameGuard\\ggsrv25.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:GameGuard\\ggsrv25.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|D:\\Project\\NDServer\\Item\\Item_Manager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A5EF0E96-FE77-40DB-8679-802927667648}|NDServer_UsingPacketQueue.vcxproj|solutionrelative:Item\\Item_Manager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Environment_Read.h", "DocumentMoniker": "D:\\Project\\NDServer\\Environment\\Environment_Read.h", "RelativeDocumentMoniker": "Environment\\Environment_Read.h", "ToolTip": "D:\\Project\\NDServer\\Environment\\Environment_Read.h", "RelativeToolTip": "Environment\\Environment_Read.h", "ViewState": "AgIAAMYBAAAAAAAAAAAswNQBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-13T04:53:40.156Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "LevelManager.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\Level_Luck\\LevelManager.cpp", "RelativeDocumentMoniker": "Level_Luck\\LevelManager.cpp", "ToolTip": "D:\\Project\\NDServer\\Level_Luck\\LevelManager.cpp", "RelativeToolTip": "Level_Luck\\LevelManager.cpp", "ViewState": "AgIAAGwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-04-13T04:53:14.356Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "SkillManager.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\Skill\\SkillManager.cpp", "RelativeDocumentMoniker": "Skill\\SkillManager.cpp", "ToolTip": "D:\\Project\\NDServer\\Skill\\SkillManager.cpp", "RelativeToolTip": "Skill\\SkillManager.cpp", "ViewState": "AgIAANoIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-04-13T04:51:40.627Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ExceptionBattle.h", "DocumentMoniker": "D:\\Project\\NDServer\\User\\ExceptionBattle.h", "RelativeDocumentMoniker": "User\\ExceptionBattle.h", "ToolTip": "D:\\Project\\NDServer\\User\\ExceptionBattle.h", "RelativeToolTip": "User\\ExceptionBattle.h", "ViewState": "AgIAAO4BAAAAAAAAAAAuwHsBAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2024-04-01T08:29:46.121Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "packet_for_ex_battle_room.h", "DocumentMoniker": "D:\\Project\\NDServer\\Packets\\packet_for_ex_battle_room.h", "RelativeDocumentMoniker": "Packets\\packet_for_ex_battle_room.h", "ToolTip": "D:\\Project\\NDServer\\Packets\\packet_for_ex_battle_room.h", "RelativeToolTip": "Packets\\packet_for_ex_battle_room.h", "ViewState": "AQIAAMMAAAAAAAAAAADwvwAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2024-04-01T08:28:42.322Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "User.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\User\\User.cpp", "RelativeDocumentMoniker": "User\\User.cpp", "ToolTip": "D:\\Project\\NDServer\\User\\User.cpp", "RelativeToolTip": "User\\User.cpp", "ViewState": "AQIAALoBAAAAAAAAAAAcwMYBAAAaAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:27:07.399Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "ExceptionBattle.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\User\\ExceptionBattle.cpp", "RelativeDocumentMoniker": "User\\ExceptionBattle.cpp", "ToolTip": "D:\\Project\\NDServer\\User\\ExceptionBattle.cpp", "RelativeToolTip": "User\\ExceptionBattle.cpp", "ViewState": "AQIAAC8BAAAAAAAAAAAuwDwBAAAhAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:27:04.996Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "User_Item_Part.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\User\\User_Item_Part.cpp", "RelativeDocumentMoniker": "User\\User_Item_Part.cpp", "ToolTip": "D:\\Project\\NDServer\\User\\User_Item_Part.cpp", "RelativeToolTip": "User\\User_Item_Part.cpp", "ViewState": "AQIAACgAAAAAAAAAAAAuwDUAAAAOAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:26:09.245Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Environment_Read.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\Environment\\Environment_Read.cpp", "RelativeDocumentMoniker": "Environment\\Environment_Read.cpp", "ToolTip": "D:\\Project\\NDServer\\Environment\\Environment_Read.cpp", "RelativeToolTip": "Environment\\Environment_Read.cpp", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAkAAAAOAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:25:58.93Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "NineDragonsServer.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\NineDragonsServer.cpp", "RelativeDocumentMoniker": "NineDragonsServer.cpp", "ToolTip": "D:\\Project\\NDServer\\NineDragonsServer.cpp", "RelativeToolTip": "NineDragonsServer.cpp", "ViewState": "AQIAAKMDAAAAAAAAAAAuwLADAAAjAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:25:51.462Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "SNpcManager.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\NPC\\SNpcManager.cpp", "RelativeDocumentMoniker": "NPC\\SNpcManager.cpp", "ToolTip": "D:\\Project\\NDServer\\NPC\\SNpcManager.cpp", "RelativeToolTip": "NPC\\SNpcManager.cpp", "ViewState": "AQIAABwAAAAAAAAAAAAuwCkAAAAOAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:25:43.457Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Global_Function.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\Global\\Global_Function.cpp", "RelativeDocumentMoniker": "Global\\Global_Function.cpp", "ToolTip": "D:\\Project\\NDServer\\Global\\Global_Function.cpp", "RelativeToolTip": "Global\\Global_Function.cpp", "ViewState": "AQIAAKYFAAAAAAAAAAAuwIYFAAAHAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:25:21.683Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "Global_Define.h", "DocumentMoniker": "D:\\Project\\NDServer\\Global\\Global_Define.h", "RelativeDocumentMoniker": "Global\\Global_Define.h", "ToolTip": "D:\\Project\\NDServer\\Global\\Global_Define.h", "RelativeToolTip": "Global\\Global_Define.h", "ViewState": "AQIAAPoAAAAAAAAAAAAuwAcBAAAIAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2024-04-01T08:24:36.795Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "FileInput.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\Global\\FileInput.cpp", "RelativeDocumentMoniker": "Global\\FileInput.cpp", "ToolTip": "D:\\Project\\NDServer\\Global\\FileInput.cpp", "RelativeToolTip": "Global\\FileInput.cpp", "ViewState": "AQIAABsAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:24:28.394Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "ggsrv25.h", "DocumentMoniker": "D:\\Project\\NDServer\\GameGuard\\ggsrv25.h", "RelativeDocumentMoniker": "GameGuard\\ggsrv25.h", "ToolTip": "D:\\Project\\NDServer\\GameGuard\\ggsrv25.h", "RelativeToolTip": "GameGuard\\ggsrv25.h", "ViewState": "AQIAAIcAAAAAAAAAAADwvwAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2024-04-01T08:24:15.003Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "ggsrv25.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\GameGuard\\ggsrv25.cpp", "RelativeDocumentMoniker": "GameGuard\\ggsrv25.cpp", "ToolTip": "D:\\Project\\NDServer\\GameGuard\\ggsrv25.cpp", "RelativeToolTip": "GameGuard\\ggsrv25.cpp", "ViewState": "AQIAAAAAAAAAAAAAAADwvwAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:24:09.539Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "Item_Manager.cpp", "DocumentMoniker": "D:\\Project\\NDServer\\Item\\Item_Manager.cpp", "RelativeDocumentMoniker": "Item\\Item_Manager.cpp", "ToolTip": "D:\\Project\\NDServer\\Item\\Item_Manager.cpp", "RelativeToolTip": "Item\\Item_Manager.cpp", "ViewState": "AQIAAB4AAAAAAAAAAAAswDIAAAAQAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-04-01T08:23:20.795Z"}]}]}]}