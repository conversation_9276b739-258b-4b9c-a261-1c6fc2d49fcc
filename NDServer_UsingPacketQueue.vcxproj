<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Rd_ACCLAIM|Win32">
      <Configuration>Rd_ACCLAIM</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Rd_IDC|Win32">
      <Configuration>Rd_IDC</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Rd_METEL|Win32">
      <Configuration>Rd_METEL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Rd_NX_IN_TS|Win32">
      <Configuration>Rd_NX_IN_TS</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Rd_OPEN_TEST|Win32">
      <Configuration>Rd_OPEN_TEST</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Rd_OT_INTS|Win32">
      <Configuration>Rd_OT_INTS</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Rd_ROC|Win32">
      <Configuration>Rd_ROC</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Rd_RUSSIA|Win32">
      <Configuration>Rd_RUSSIA</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Rd_VINA|Win32">
      <Configuration>Rd_VINA</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>"$/NDServer_UsingPacketQueue", MDBAAAAA</SccProjectName>
    <SccLocalPath>.</SccLocalPath>
    <ProjectGuid>{A5EF0E96-FE77-40DB-8679-************}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_ACCLAIM|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v70</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_RUSSIA|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_IDC|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_VINA|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_ROC|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_OPEN_TEST|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_METEL|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_NX_IN_TS|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_OT_INTS|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_ACCLAIM|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_RUSSIA|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_IDC|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_VINA|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_ROC|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_OPEN_TEST|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_METEL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_NX_IN_TS|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Rd_OT_INTS|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_OT_INTS|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_OT_INTS\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_OT_INTS\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_NX_IN_TS|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_METEL|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_METEL\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_METEL\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_OPEN_TEST|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_ROC|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_ROC\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_ROC\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_VINA|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_VINA0\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_VINA0\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_IDC|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_IDC\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_IDC\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_RUSSIA|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_RUSSIA\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_RUSSIA\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Rd_ACCLAIM|Win32'">
    <OutDir>.\NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM\</OutDir>
    <IntDir>.\NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\Release\</OutDir>
    <IntDir>.\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_OT_INTS|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_PD_OT_INTS_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_OT_INTS\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_OT_INTS\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_OT_INTS\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_OT_INTS\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_OT_INTS\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_OT_INTS\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_ot_ints.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_NX_IN_TS|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_PD_COMPANY_NXINTS_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_NX_IN_TS\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_nxints.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_METEL|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_COMPANY_METEL_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_METEL\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_METEL\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_METEL\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_METEL\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_METEL\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_METEL\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_Metel.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_OPEN_TEST|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_PD_OPEN_TEST_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_OPEN_TEST\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_opentest.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_ROC|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_COMPANY_JAPAN_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_ROC\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_ROC\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_ROC\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_ROC\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_ROC\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_ROC\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_RocWorks.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_VINA|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_COMPANY_VINA_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_VINA0\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_VINA0\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_VINA0\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_VINA0\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_VINA0\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_VINA0\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_Vina.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_IDC|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_PD_COMPANY_NEXON_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_IDC\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_IDC\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_IDC\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_IDC\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_IDC\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_IDC\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_idc.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_RUSSIA|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_COMPANY_RUSSIA_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_RUSSIA\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_RUSSIA\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_RUSSIA\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_RUSSIA\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_RUSSIA\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_RUSSIA\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_russia.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Rd_ACCLAIM|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_COMPANY_ACCLAIM_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM\</ObjectFileName>
      <ProgramDataBaseFileName>.\NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\NDServer_UsingPacketQueue___Win32_Rd_ACCLAIM\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OutputFile>Release/NDServer_acclaim.exe</OutputFile>
      <AdditionalDependencies>winmm.lib;ws2_32.lib;Iphlpapi.lib;Dbghelp.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_TRACER_ON_;_INNER_TEST_;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Release\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\Release\NDServer_UsingPacketQueue.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\Release\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <TypeLibraryName>.\Release\NDServer_UsingPacketQueue.tlb</TypeLibraryName>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release\NDServer_UsingPacketQueue.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <SubSystem>Console</SubSystem>
      <OutputFile>.\Release\NDServer_UsingPacketQueue.exe</OutputFile>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;winmm.lib;ws2_32.lib;Iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="Acclaim\AD\Acclaim_AD_RewardListParser.h" />
    <ClInclude Include="Apex\ApexProxy.h" />
    <ClInclude Include="Apex\NDApexFunction.h" />
    <ClInclude Include="Crypto\crypto.h" />
    <ClInclude Include="DNPC_Manager\ckAllocedPool.h" />
    <ClInclude Include="DNPC_Manager\ckDNPC.h" />
    <ClInclude Include="DNPC_Manager\ckDNPCAllocedPool.h" />
    <ClInclude Include="DNPC_Manager\ckDNPCMacro.h" />
    <ClInclude Include="DNPC_Manager\ckDNPCMessage.h" />
    <ClInclude Include="DNPC_Manager\ckDNPCMessageQueue.h" />
    <ClInclude Include="DNPC_Manager\ckDNPCPathManager.h" />
    <ClInclude Include="DNPC_Manager\ckDNPCPool.h" />
    <ClInclude Include="DNPC_Manager\ckDNPCProcThread.h" />
    <ClInclude Include="DNPC_Manager\ckDNPCSpecies.h" />
    <ClInclude Include="DNPC_Manager\ckSpawnInfo.h" />
    <ClInclude Include="Environment\Environment_Read.h" />
    <ClInclude Include="Event\GEvent\GameEvent.h" />
    <ClInclude Include="Event\GEvent\GameEventParser.h" />
    <ClInclude Include="Event\GEvent\GEventStructure.h" />
    <ClInclude Include="Event\PKEvent\PkEvent.h" />
    <ClInclude Include="Event\PKEvent\PkEventScriptParser.h" />
    <ClInclude Include="GameGuard\ggsrv25.h" />
    <ClInclude Include="GameGuard_New0228\ggsrv25.h" />
    <ClInclude Include="Global\Billing_Define.h" />
    <ClInclude Include="Global\ckSingleton.h" />
    <ClInclude Include="Global\ComTcp_packets.h" />
    <ClInclude Include="Global\CriticalSection.h" />
    <ClInclude Include="Global\eod_base_file.h" />
    <ClInclude Include="Global\FileInput.h" />
    <ClInclude Include="Global\FileLog.h" />
    <ClInclude Include="Global\gDefine.h" />
    <ClInclude Include="Global\gGlobal.h" />
    <ClInclude Include="Global\Global_Define.h" />
    <ClInclude Include="Global\Global_Function.h" />
    <ClInclude Include="Global\NDThread.h" />
    <ClInclude Include="Global\NDUtilClass.h" />
    <ClInclude Include="hdr_acclaim.h" />
    <ClInclude Include="hdr_japan.h" />
    <ClInclude Include="hdr_metel.h" />
    <ClInclude Include="hdr_nexon.h" />
    <ClInclude Include="hdr_nx_innertest.h" />
    <ClInclude Include="hdr_open_test.h" />
    <ClInclude Include="hdr_ot_inner.h" />
    <ClInclude Include="hdr_russia.h" />
    <ClInclude Include="hdr_vina.h" />
    <ClInclude Include="Item\Item.h" />
    <ClInclude Include="Item\Item_Manager.h" />
    <ClInclude Include="Item\PkgMgr.h" />
    <ClInclude Include="Item\ScriptParser.h" />
    <ClInclude Include="Level_Luck\LevelManager.h" />
    <ClInclude Include="LogSvr\Global_Log_Define.h" />
    <ClInclude Include="LogSvr\Global_Log_Packet.h" />
    <ClInclude Include="LogSvr\LogSvrLink.h" />
    <ClInclude Include="LogWnd\LogWnd.h" />
    <ClInclude Include="NDExceptionReport\NDExceptionReport.h" />
    <ClInclude Include="Network\Network.h" />
    <ClInclude Include="NPC\CollectNPC.h" />
    <ClInclude Include="NPC\NPCBase.h" />
    <ClInclude Include="NPC\SNpcManager.h" />
    <ClInclude Include="NPC\StaticNpc.h" />
    <ClInclude Include="PacketQueue\Packet_Queue.h" />
    <ClInclude Include="Packets\Game_packets.h" />
    <ClInclude Include="Packets\Packet_for_Battle.h" />
    <ClInclude Include="Packets\packet_for_combat.h" />
    <ClInclude Include="Packets\packet_for_Common.h" />
    <ClInclude Include="Packets\packet_for_ctrl_gms.h" />
    <ClInclude Include="Packets\Packet_for_Event.h" />
    <ClInclude Include="Packets\packet_for_ex_battle_room.h" />
    <ClInclude Include="Packets\packet_for_GGAuth.h" />
    <ClInclude Include="Packets\Packet_for_GMS.h" />
    <ClInclude Include="Packets\Packet_for_ItemShop.h" />
    <ClInclude Include="Packets\packet_for_items_trade.h" />
    <ClInclude Include="Packets\packet_for_Level.h" />
    <ClInclude Include="Packets\packet_for_LifeEtc.h" />
    <ClInclude Include="Packets\packet_for_lobby.h" />
    <ClInclude Include="Packets\packet_for_login.h" />
    <ClInclude Include="Packets\packet_for_nic.h" />
    <ClInclude Include="Packets\packet_for_organization.h" />
    <ClInclude Include="Packets\packet_for_party.h" />
    <ClInclude Include="Packets\packet_for_quest.h" />
    <ClInclude Include="Packets\packet_for_skill.h" />
    <ClInclude Include="Packets\packet_for_svrmove.h" />
    <ClInclude Include="Packets\packet_for_Zone_Monster.h" />
    <ClInclude Include="PathFinder\PathFinder.h" />
    <ClInclude Include="Quest\Quest_Define.h" />
    <ClInclude Include="Quest\ServerQuest.h" />
    <ClInclude Include="Quest\user_quest.h" />
    <ClInclude Include="Ranking\Ranking.h" />
    <ClInclude Include="Scheduler\Scheduler.h" />
    <ClInclude Include="Server Communication\Server_Com.h" />
    <ClInclude Include="Shutdown\FatiguePenaltyListParser.h" />
    <ClInclude Include="Skill\SkillManager.h" />
    <ClInclude Include="SmallNet\Packet.h" />
    <ClInclude Include="SmallNet\PktMgr.h" />
    <ClInclude Include="SmallNet\PktProc\Echo.h" />
    <ClInclude Include="SmallNet\PktProc\PktBilling.h" />
    <ClInclude Include="SmallNet\PktProc\_PktDef.h" />
    <ClInclude Include="SmallNet\PktQue.h" />
    <ClInclude Include="SmallNet\SmallNet.h" />
    <ClInclude Include="SmallNet\Sock.h" />
    <ClInclude Include="StdAfx.h" />
    <ClInclude Include="User\Combat.h" />
    <ClInclude Include="User\CombatModule.h" />
    <ClInclude Include="User\CombatProc.h" />
    <ClInclude Include="User\CombatRecord.h" />
    <ClInclude Include="User\CombatStruct.h" />
    <ClInclude Include="User\ExceptionBattle.h" />
    <ClInclude Include="User\LocationStruct.h" />
    <ClInclude Include="User\Location_Tracking_System.h" />
    <ClInclude Include="User\MatchSystem.h" />
    <ClInclude Include="User\NDParty.h" />
    <ClInclude Include="User\Organization.h" />
    <ClInclude Include="User\TaxSystem.h" />
    <ClInclude Include="User\User.h" />
    <ClInclude Include="User\UserSkill.h" />
    <ClInclude Include="User\UserTimer.h" />
    <ClInclude Include="VirtualMemory\VMManager.h" />
    <ClInclude Include="Zone\Zone.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Acclaim\AD\Acclaim_AD_RewardListParser.cpp" />
    <ClCompile Include="Apex\ApexProxy.cpp" />
    <ClCompile Include="Apex\NDApexFunction.cpp" />
    <ClCompile Include="DNPC_Manager\ckDNPC.cpp" />
    <ClCompile Include="DNPC_Manager\ckDNPC2.cpp" />
    <ClCompile Include="DNPC_Manager\ckDNPCAllocedPool.cpp" />
    <ClCompile Include="DNPC_Manager\ckDNPCMacro.cpp" />
    <ClCompile Include="DNPC_Manager\ckDNPCMessageQueue.cpp" />
    <ClCompile Include="DNPC_Manager\ckDNPCPathManager.cpp" />
    <ClCompile Include="DNPC_Manager\ckDNPCPool.cpp" />
    <ClCompile Include="DNPC_Manager\ckDNPCProcThread.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_00_00_Type_70.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_02_01_Type_101.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_02_02_Type_102.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_02_03_Type_103.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_02_04_Type_104.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_01_Type_105.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_02_Type_106.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_03_Type_107.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_04_Type_108.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_05_Type_109.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_06_Type_110.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_07_Type_111.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_08_Type_112.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_09_Type_113.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_102_Type_206.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_104_Type_208.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_10_Type_114.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_11_Type_115.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_12_Type_116.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_13_Type_117.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_14_Type_118.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_15_Type_119.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_16_Type_120.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_17_Type_121.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_22_Type_126.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_23_Type_127.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_25_Type_129.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_26_Type_130.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_27_Type_131.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_30_Type_134.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_40_Type_144.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_50_Type_154.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_53_Type_157.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_54_Type_158.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_57_Type_161.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_59_Type_163.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_60_Type_164.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_61_Type_165.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_63_Type_167.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_69_Type_173.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_70_Type_174.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_74_Type_178.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_75_Type_179.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_03_99_Type_203.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_04_Type_219.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_05_Type_220.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_07_Type_222.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_12_Type_227.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_05_20_Type_235.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_06_01_Type_238.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_06_05_Type_242.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_07_01A_ISTJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_08_01B_ISTJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_09_03_Type_262.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_09_04_Type_263.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_09_12_Type_271.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_09_NewType.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_10_01_Type_273.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_10_02B_ISFJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_11_03A_INFJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_12_03B_INFJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_13_04A_INTJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_14_04B_INTJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_16_05B_ISTP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_18_06B_ISFP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_19_07A_INFP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_20_07B_INFP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_21_08A_INTP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_22_08B_INTP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_23_09A_ESTP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_24_09B_ESTP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_25_10A_ESFP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_26_10B_ESFP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_277_die_help_Human.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_278_Heal.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_279_SP1.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_27_11A_ENFP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_28_11B_ENFP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_29_12A_ENTP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_30_12B_ENTP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_31_13A_ESTJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_33_14A_ESFJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_35_15A_ENFJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_36_15B_ENFJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_37_16A_ENTJ.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_40_Heal_Type.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_42_AI_02_Type.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_51_Raid_Top.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_52_Raid_Middle.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_53_Tolerance.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_70_manor.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_81_Normal.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_82_Key.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_83_Transform.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_84_HP.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_85_Aggressive.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_86_AggressiveDummy.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_90_Object.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_96_Patrol06_Monster.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Bear.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_BloodWolf.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Fox.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Leopard.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Tiger.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Animal_Wolf.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_bookhae_field_boss.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_bookhae_field_raid_a.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_bookhae_field_raid_b.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_bookhae_guard_monster.cpp" />
    <ClCompile Include="DNPC_Manager\DNPC_Script\DNPC_Patrol01.cpp" />
    <ClCompile Include="Environment\Environment_Read.cpp" />
    <ClCompile Include="Event\GEvent\GameEvent.cpp" />
    <ClCompile Include="Event\GEvent\GameEventParser.cpp" />
    <ClCompile Include="Event\GEvent\GEventStructure.cpp" />
    <ClCompile Include="Event\PKEvent\PkEvent.cpp" />
    <ClCompile Include="Event\PKEvent\PkEventScriptParser.cpp" />
    <ClCompile Include="GameGuard\ggsrv25.cpp" />
    <ClCompile Include="GameGuard_New0228\ggsrv25_2.cpp" />
    <ClCompile Include="Global\eod_base_file.cpp" />
    <ClCompile Include="Global\FileInput.cpp" />
    <ClCompile Include="Global\Global_Function.cpp" />
    <ClCompile Include="Global\NDThread.cpp" />
    <ClCompile Include="Global\NDUtilClass.cpp" />
    <ClCompile Include="Item\Item_Manager.cpp" />
    <ClCompile Include="Item\PkgMgr.cpp" />
    <ClCompile Include="Item\ScriptParser_ItemShop.cpp" />
    <ClCompile Include="Level_Luck\LevelManager.cpp" />
    <ClCompile Include="LogSvr\LogSave_Thread.cpp" />
    <ClCompile Include="LogSvr\LogSvrLink.cpp" />
    <ClCompile Include="NDExceptionReport\NDExceptionReport.cpp" />
    <ClCompile Include="Network\Network.cpp" />
    <ClCompile Include="NineDragonsServer.cpp" />
    <ClCompile Include="NPC\CollectNPC.cpp" />
    <ClCompile Include="NPC\SNpcManager.cpp" />
    <ClCompile Include="NPC\StaticNpc.cpp" />
    <ClCompile Include="PacketProcess\PING_Thread.cpp" />
    <ClCompile Include="PacketProcess\Proc_General.cpp" />
    <ClCompile Include="PacketProcess\Proc_Items.cpp" />
    <ClCompile Include="PacketProcess\Proc_LevelSkill.cpp" />
    <ClCompile Include="PacketProcess\Proc_Lobby.cpp" />
    <ClCompile Include="PacketProcess\Proc_Quest.cpp" />
    <ClCompile Include="PacketQueue\Packet_Queue.cpp" />
    <ClCompile Include="PathFinder\PathFinder.cpp" />
    <ClCompile Include="Quest\ServerQuest.cpp" />
    <ClCompile Include="Quest\user_quest.cpp" />
    <ClCompile Include="Quest\user_quest2.cpp" />
    <ClCompile Include="Ranking\Ranking.cpp" />
    <ClCompile Include="restore_contents.cpp" />
    <ClCompile Include="Scheduler\Scheduler.cpp" />
    <ClCompile Include="Server Communication\Server_Com.cpp" />
    <ClCompile Include="Server Communication\Server_Com_TCP.cpp" />
    <ClCompile Include="Shutdown\FatiguePenaltyListParser.cpp" />
    <ClCompile Include="Skill\SkillManager.cpp" />
    <ClCompile Include="SmallNet\PktMgr.cpp" />
    <ClCompile Include="SmallNet\PktProc\Echo.cpp" />
    <ClCompile Include="SmallNet\PktProc\NetEventProc.cpp" />
    <ClCompile Include="SmallNet\PktProc\PktBilling.cpp" />
    <ClCompile Include="SmallNet\PktProc\_PkHdr.cpp" />
    <ClCompile Include="SmallNet\PktQue.cpp" />
    <ClCompile Include="SmallNet\SmallNet.cpp" />
    <ClCompile Include="SmallNet\Sock.cpp" />
    <ClCompile Include="User\Combat.cpp" />
    <ClCompile Include="User\CombatModule.cpp" />
    <ClCompile Include="User\CombatProc.cpp" />
    <ClCompile Include="User\CombatRecord.cpp" />
    <ClCompile Include="User\ExceptionBattle.cpp" />
    <ClCompile Include="User\Location_Tracking_System.cpp" />
    <ClCompile Include="User\MatchSystem.cpp" />
    <ClCompile Include="User\NDParty.cpp" />
    <ClCompile Include="User\Organization.cpp" />
    <ClCompile Include="User\TaxSystem.cpp" />
    <ClCompile Include="User\User.cpp" />
    <ClCompile Include="User\UserSkill.cpp" />
    <ClCompile Include="User\UserTimer.cpp" />
    <ClCompile Include="User\User_Battle_Part.cpp" />
    <ClCompile Include="User\User_Effect_Part.cpp" />
    <ClCompile Include="User\User_Item_Part.cpp" />
    <ClCompile Include="VirtualMemory\VMManager.cpp" />
    <ClCompile Include="Zone\Zone.cpp" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="Crypto\lump.dat" />
    <CustomBuild Include="mFileLog.dll" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="LogWnd\mLogWnd.lib" />
    <Library Include="LogWnd\uLogWnd.lib" />
    <Library Include="mFileLog.lib" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="nfofile\Acclaim_AD_RewardList.txt" />
    <Text Include="ReadMe.txt" />
    <Text Include="Shutdown\FatiguePenaltyList.txt" />
    <Text Include="Temp.txt" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>