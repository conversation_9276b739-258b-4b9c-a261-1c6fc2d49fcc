#pragma once

#pragma pack( push, q98asfas )
#pragma pack(1)

#include "Global_Log_Define.h"

#define LMSG_NO_SERVER_STATE					1
struct MSG_SERVER_STATE {
	u_short		sLength ;	
	u_char		cMessage ;
	
	_ServerState		log_ServerState ;
};

#define LMSG_NO_ITEM						2
struct MSG_ITEM {
	u_short		sLength ;	
	u_char		cMessage ;

	_ItemLog	log_Item ;
	
};


#define LMSG_NO_CHARACTER				3
struct MSG_CHARACTER {
	u_short		sLength ;	
	u_char		cMessage ;

	_CharacLog	log_Charac ;
};


#pragma pack( pop, q98asfas )